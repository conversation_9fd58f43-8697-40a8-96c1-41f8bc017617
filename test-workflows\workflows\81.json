{"createdAt": "2021-03-01T09:51:34.123Z", "updatedAt": "2021-06-02T11:29:56.485Z", "id": "81", "name": "PhantomBuster:getAll get launch getOutput", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "85fb6aa2-9869-4db4-bd9a-daf1db2bf96e"}, {"parameters": {"operation": "getAll", "limit": 1}, "name": "Phantombuster", "type": "n8n-nodes-base.phantombuster", "typeVersion": 1, "position": [450, 300], "credentials": {"phantombusterApi": {"id": "70", "name": "Phantombuster creds"}}, "id": "1b0bda87-f6ec-42a9-84db-af15ec98c377"}, {"parameters": {"operation": "get", "agentId": "={{$node[\"Phantombuster\"].json[\"id\"]}}"}, "name": "Phantombuster1", "type": "n8n-nodes-base.phantombuster", "typeVersion": 1, "position": [600, 300], "credentials": {"phantombusterApi": {"id": "70", "name": "Phantombuster creds"}}, "id": "1c0d3094-59c7-4f8c-b7d6-3544dcde6bcc"}, {"parameters": {"agentId": "={{$node[\"Phantombuster\"].json[\"id\"]}}", "resolveData": false, "jsonParameters": true, "additionalFields": {"argumentsJson": "{\n\t\"urls\": [\n\t\t\"https://n8n.io/\"\n\t],\n\t\"timeToWait\": 5000,\n\t\"pagesPerLaunch\": 1,\n\t\"onlyGetOneEmail\": false\n}"}}, "name": "Phantombuster2", "type": "n8n-nodes-base.phantombuster", "typeVersion": 1, "position": [750, 300], "credentials": {"phantombusterApi": {"id": "70", "name": "Phantombuster creds"}}, "id": "932b8b9f-b118-401c-9164-aac9942631a9"}, {"parameters": {"operation": "getOutput", "agentId": "={{$node[\"Phantombuster\"].json[\"id\"]}}", "resolveData": false, "additionalFields": {"prevContainerId": "={{$node[\"Phantombuster2\"].json[\"containerId\"]}}"}}, "name": "Phantombuster3", "type": "n8n-nodes-base.phantombuster", "typeVersion": 1, "position": [900, 300], "credentials": {"phantombusterApi": {"id": "70", "name": "Phantombuster creds"}}, "notes": "IGNORED_PROPERTIES=mostRecentEndedAt", "id": "803d6c12-a407-4037-ae9f-62b90d17ceac"}], "connections": {"Phantombuster": {"main": [[{"node": "Phantombuster1", "type": "main", "index": 0}]]}, "Phantombuster1": {"main": [[{"node": "Phantombuster2", "type": "main", "index": 0}]]}, "Phantombuster2": {"main": [[{"node": "Phantombuster3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Phantombuster", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}