{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344011791, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Set": [{"startTime": 1747344011791, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"name": "test"}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1747344011791, "executionIndex": 2, "source": [{"previousNode": "Set"}], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {"name": "test"}, "pairedItem": {"item": 0}, "index": 0}]]}}]}, "lastNodeExecuted": "Function"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:11.791Z", "stoppedAt": "2025-05-15T21:20:11.792Z", "status": "running", "finished": true}