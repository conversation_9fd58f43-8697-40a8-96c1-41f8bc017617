{"createdAt": "2021-02-15T16:15:47.074Z", "updatedAt": "2021-06-04T14:24:35.242Z", "id": "11", "name": "Mailchimp:Member:getall get create update delete:Member Tag:create delete:ListGroup:getAll:Campaign:getAll get replicate delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [160, 300], "id": "d8d987a3-cc5c-4ec0-b011-7bc16e31ca08"}, {"parameters": {"operation": "getAll", "list": "eb9ad4be19", "limit": 1, "options": {}}, "name": "Mailchimp1", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [430, 300], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "320b1a49-4754-426d-92ea-e01b24a9f4c6"}, {"parameters": {"operation": "get", "list": "eb9ad4be19", "email": "<EMAIL>", "options": {}}, "name": "Mailchimp", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [590, 300], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "1abaa505-82e8-47c0-89f1-b66c9f9f3d22"}, {"parameters": {"operation": "update", "list": "eb9ad4be19", "email": "={{$json[\"email_address\"]}}", "updateFields": {"emailType": "html"}}, "name": "Mailchimp2", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [780, 300], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "54452790-7916-4f94-9e9a-91e0e87a5267"}, {"parameters": {"resource": "memberTag", "list": "eb9ad4be19", "email": "<EMAIL>", "tags": ["n8n"], "options": {}}, "name": "Mailchimp4", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [790, 140], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "notes": "CAP_RESULTS_LENGTH=1", "id": "f6b3215e-4628-4118-b994-e3f568f0dd61"}, {"parameters": {"resource": "memberTag", "operation": "delete", "list": "eb9ad4be19", "email": "<EMAIL>", "tags": ["n8n"], "options": {}}, "name": "Mailchimp5", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [970, 140], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "notes": "CAP_RESULTS_LENGTH=1", "id": "9e1dafb8-8afd-4b0d-9623-65b5fb6c1322"}, {"parameters": {"list": "eb9ad4be19", "email": "={{$json[\"email\"]}}", "status": "subscribed", "options": {}}, "name": "Mailchimp3", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [570, 450], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "197d2f33-acf1-433f-bf88-c44e076539c5"}, {"parameters": {"functionCode": "items = [{\n  json:{\n    email: `test${Date.now().toString().substr(5)}@gmail.com`\n  }\n}\n]\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [430, 450], "notesInFlow": true, "notes": "Generate fake email", "id": "9173b153-2d71-41c5-88d3-34a73d187e1b"}, {"parameters": {"operation": "delete", "list": "eb9ad4be19", "email": "={{$json[\"email_address\"]}}"}, "name": "Mailchimp6", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [730, 450], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "60e1eb5b-1725-4b11-a22f-27317dcbee60"}, {"parameters": {"resource": "listGroup", "list": "eb9ad4be19", "groupCategory": "2adbc0d543", "limit": 1}, "name": "Mailchimp7", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [430, 140], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "57698c39-732e-43ab-b29b-abc24eeab04b"}, {"parameters": {"resource": "campaign", "limit": 1, "options": {}}, "name": "Mailchimp8", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [430, 610], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "b24cf3d7-b5bf-4386-af74-f1b20fec54f0"}, {"parameters": {"resource": "campaign", "operation": "get", "campaignId": "={{$node[\"Mailchimp8\"].json[\"id\"]}}"}, "name": "Mailchimp9", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [580, 610], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "3a7e9e1a-f070-4c89-9f27-77e9338afca5"}, {"parameters": {"resource": "campaign", "operation": "replicate", "campaignId": "={{$node[\"Mailchimp8\"].json[\"id\"]}}"}, "name": "Mailchimp10", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [720, 610], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "3c7ab34b-7762-4c36-aeb1-2c51e9a27c88"}, {"parameters": {"resource": "campaign", "operation": "delete", "campaignId": "={{$node[\"Mailchimp10\"].json[\"id\"]}}"}, "name": "Mailchimp11", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [870, 610], "credentials": {"mailchimpApi": {"id": "29", "name": "Mailchimp creds"}}, "id": "34e5824a-841b-4890-b5d9-7d72faa17bfc"}], "connections": {"Mailchimp1": {"main": [[{"node": "Mailchimp", "type": "main", "index": 0}]]}, "Mailchimp": {"main": [[{"node": "Mailchimp2", "type": "main", "index": 0}, {"node": "Mailchimp4", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Mailchimp1", "type": "main", "index": 0}, {"node": "Function", "type": "main", "index": 0}, {"node": "Mailchimp7", "type": "main", "index": 0}, {"node": "Mailchimp8", "type": "main", "index": 0}]]}, "Mailchimp4": {"main": [[{"node": "Mailchimp5", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Mailchimp3", "type": "main", "index": 0}]]}, "Mailchimp3": {"main": [[{"node": "Mailchimp6", "type": "main", "index": 0}]]}, "Mailchimp8": {"main": [[{"node": "Mailchimp9", "type": "main", "index": 0}]]}, "Mailchimp9": {"main": [[{"node": "Mailchimp10", "type": "main", "index": 0}]]}, "Mailchimp10": {"main": [[{"node": "Mailchimp11", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}