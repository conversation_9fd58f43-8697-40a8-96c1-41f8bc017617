{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"hints": [], "startTime": 1738078278998, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign": [{"hints": [], "startTime": 1738078278998, "executionTime": 4838, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"email": "<EMAIL>", "cdate": "2025-01-28T09:31:23-06:00", "udate": "2025-01-28T09:31:23-06:00", "phone": "", "orgid": "0", "orgname": "", "links": {"object": true}, "id": "6713", "organization": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign9": [{"hints": [], "startTime": 1738078283837, "executionTime": 642, "source": [{"previousNode": "ActiveCampaign"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "Name1738078283838", "createdTimestamp": "2025-01-28T09:31:24-06:00", "updatedTimestamp": "2025-01-28T09:31:24-06:00", "owner": "1", "links": {"object": true}, "fields": ["json array"], "id": "2126"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign14": [{"hints": [], "startTime": *************, "executionTime": 528, "source": [{"previousNode": "ActiveCampaign9"}], "executionStatus": "success", "data": {"main": [[{"json": {"contact": "6713", "account": "2126", "createdTimestamp": "2025-01-28T09:31:24-06:00", "updatedTimestamp": "2025-01-28T09:31:24-06:00", "links": {"object": true}, "id": "1829"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign15": [{"hints": [], "startTime": *************, "executionTime": 897, "source": [{"previousNode": "ActiveCampaign14"}], "executionStatus": "success", "data": {"main": [[{"json": {"account": "2126", "contact": "6713", "jobTitle": "TestJobTitle", "createdTimestamp": "2025-01-28T09:31:24-06:00", "updatedTimestamp": "2025-01-28T09:31:25-06:00", "links": {"object": true}, "id": "1829"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign16": [{"hints": [], "startTime": *************, "executionTime": 786, "source": [{"previousNode": "ActiveCampaign15"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign10": [{"hints": [], "startTime": *************, "executionTime": 763, "source": [{"previousNode": "ActiveCampaign16"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "updatedName1738078286699", "accountUrl": "", "createdTimestamp": "2025-01-28T09:31:24-06:00", "updatedTimestamp": "2025-01-28T09:31:27-06:00", "owner": "1", "links": {"object": true}, "fields": ["json array"], "id": "2126"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign11": [{"hints": [], "startTime": *************, "executionTime": 490, "source": [{"previousNode": "ActiveCampaign10"}], "executionStatus": "success", "data": {"main": [[{"json": {"account": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign12": [{"hints": [], "startTime": *************, "executionTime": 563, "source": [{"previousNode": "ActiveCampaign11"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "Name1621002658600", "accountUrl": {"object": true}, "createdTimestamp": "2021-05-14T09:32:11-05:00", "updatedTimestamp": "2021-08-24T02:34:13-05:00", "owner": "1", "contactCount": "0", "dealCount": "0", "links": {"object": true}, "id": "48"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign13": [{"hints": [], "startTime": *************, "executionTime": 594, "source": [{"previousNode": "ActiveCampaign12"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign5": [{"hints": [], "startTime": *************, "executionTime": 719, "source": [{"previousNode": "ActiveCampaign13"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign7": [{"hints": [], "startTime": *************, "executionTime": 720, "source": [{"previousNode": "ActiveCampaign13"}], "executionStatus": "success", "data": {"main": [[{"json": {"contact": "6713", "tag": "1", "cdate": "2025-01-28T09:31:30-06:00", "links": {"object": true}, "id": "3852"}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign6": [{"hints": [], "startTime": 1738078290545, "executionTime": 700, "source": [{"previousNode": "ActiveCampaign5"}], "executionStatus": "success", "data": {"main": [[{"json": {"cdate": "2025-01-28T09:31:23-06:00", "email": "<EMAIL>", "phone": "", "firstName": "", "lastName": "", "orgid": "0", "orgname": "", "segmentio_id": "", "bounced_hard": "0", "bounced_soft": "0", "bounced_date": {"object": true}, "ip": "0", "ua": "", "hash": "8b0ad1fff5a956cf672dbcccc12b71f1", "socialdata_lastcheck": {"object": true}, "email_local": "", "email_domain": "gmail.com", "sentcnt": "0", "rating_tstamp": {"object": true}, "gravatar": "0", "deleted": "0", "anonymized": "0", "adate": {"object": true}, "udate": "2025-01-28T09:31:31-06:00", "edate": {"object": true}, "deleted_at": {"object": true}, "created_utc_timestamp": "2025-01-28 09:31:23", "updated_utc_timestamp": "2025-01-28 09:31:31", "created_timestamp": "2025-01-28 09:31:23", "updated_timestamp": "2025-01-28 09:31:31", "created_by": "0", "updated_by": "0", "mpp_tracking": "0", "last_click_date": {"object": true}, "last_open_date": {"object": true}, "last_mpp_open_date": {"object": true}, "best_send_hour": "0", "links": {"object": true}, "id": "6713", "organization": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign8": [{"hints": [], "startTime": 1738078291245, "executionTime": 732, "source": [{"previousNode": "ActiveCampaign7"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign1": [{"hints": [], "startTime": 1738078291977, "executionTime": 673, "source": [{"previousNode": "ActiveCampaign8"}], "executionStatus": "success", "data": {"main": [[{"json": {"cdate": "2025-01-28T09:31:23-06:00", "email": "<EMAIL>", "phone": "", "firstName": "", "lastName": "", "orgid": "0", "orgname": "", "segmentio_id": "", "bounced_hard": "0", "bounced_soft": "0", "bounced_date": {"object": true}, "ip": "0", "ua": "", "hash": "8b0ad1fff5a956cf672dbcccc12b71f1", "socialdata_lastcheck": {"object": true}, "email_local": "", "email_domain": "gmail.com", "sentcnt": "0", "rating_tstamp": {"object": true}, "gravatar": "0", "deleted": "0", "anonymized": "0", "adate": {"object": true}, "udate": "2025-01-28T09:31:32-06:00", "edate": {"object": true}, "deleted_at": {"object": true}, "created_utc_timestamp": "2025-01-28 09:31:23", "updated_utc_timestamp": "2025-01-28 09:31:32", "created_timestamp": "2025-01-28 09:31:23", "updated_timestamp": "2025-01-28 09:31:32", "created_by": "0", "updated_by": "0", "mpp_tracking": "0", "best_send_hour": "0", "links": {"object": true}, "id": "6713", "organization": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign2": [{"hints": [], "startTime": *************, "executionTime": 566, "source": [{"previousNode": "ActiveCampaign1"}], "executionStatus": "success", "data": {"main": [[{"json": {"contactAutomations": ["json array"], "contactLists": ["json array"], "deals": ["json array"], "fieldValues": ["json array"], "geoIps": ["json array"], "accountContacts": ["json array"], "contact": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign3": [{"hints": [], "startTime": *************, "executionTime": 489, "source": [{"previousNode": "ActiveCampaign2"}], "executionStatus": "success", "data": {"main": [[{"json": {"cdate": "2021-04-14T05:59:52-05:00", "email": "<EMAIL>", "phone": "", "firstName": "", "lastName": "", "orgid": "0", "orgname": "", "segmentio_id": "", "bounced_hard": "0", "bounced_soft": "0", "bounced_date": {"object": true}, "ip": "0", "ua": {"object": true}, "hash": "54a85e50b2a7bf141297a326bd157ca9", "socialdata_lastcheck": {"object": true}, "email_local": "", "email_domain": "a.com", "sentcnt": "0", "rating_tstamp": {"object": true}, "gravatar": "0", "deleted": "0", "anonymized": "0", "adate": {"object": true}, "udate": "2021-04-14T05:59:52-05:00", "edate": {"object": true}, "deleted_at": {"object": true}, "created_utc_timestamp": "2021-04-14 05:59:52", "updated_utc_timestamp": "2022-06-02 20:44:36", "created_timestamp": "2021-04-14 05:59:52", "updated_timestamp": "2022-06-02 20:44:36", "created_by": {"object": true}, "updated_by": {"object": true}, "mpp_tracking": "0", "last_click_date": {"object": true}, "last_open_date": {"object": true}, "last_mpp_open_date": {"object": true}, "best_send_hour": {"object": true}, "scoreValues": ["json array"], "accountContacts": ["json array"], "links": {"object": true}, "id": "97", "organization": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Merge": [{"hints": [], "startTime": *************, "executionTime": 1, "source": [{"previousNode": "ActiveCampaign3"}, {"previousNode": "ActiveCampaign6"}], "executionStatus": "success", "data": {"main": [[{"json": {"cdate": "2021-04-14T05:59:52-05:00", "email": "<EMAIL>", "phone": "", "firstName": "", "lastName": "", "orgid": "0", "orgname": "", "segmentio_id": "", "bounced_hard": "0", "bounced_soft": "0", "bounced_date": {"object": true}, "ip": "0", "ua": {"object": true}, "hash": "54a85e50b2a7bf141297a326bd157ca9", "socialdata_lastcheck": {"object": true}, "email_local": "", "email_domain": "a.com", "sentcnt": "0", "rating_tstamp": {"object": true}, "gravatar": "0", "deleted": "0", "anonymized": "0", "adate": {"object": true}, "udate": "2021-04-14T05:59:52-05:00", "edate": {"object": true}, "deleted_at": {"object": true}, "created_utc_timestamp": "2021-04-14 05:59:52", "updated_utc_timestamp": "2022-06-02 20:44:36", "created_timestamp": "2021-04-14 05:59:52", "updated_timestamp": "2022-06-02 20:44:36", "created_by": {"object": true}, "updated_by": {"object": true}, "mpp_tracking": "0", "last_click_date": {"object": true}, "last_open_date": {"object": true}, "last_mpp_open_date": {"object": true}, "best_send_hour": {"object": true}, "scoreValues": ["json array"], "accountContacts": ["json array"], "links": {"object": true}, "id": "97", "organization": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ActiveCampaign4": [{"hints": [], "startTime": *************, "executionTime": 796, "source": [{"previousNode": "<PERSON><PERSON>"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "ActiveCampaign4"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-01-28T15:31:18.996Z", "stoppedAt": "2025-01-28T15:31:34.502Z", "status": "running", "finished": true}