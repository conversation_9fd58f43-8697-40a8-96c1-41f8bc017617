{"createdAt": "2021-02-25T13:44:10.456Z", "updatedAt": "2021-02-25T13:44:14.960Z", "id": "71", "name": "Contentful-preview-api:locale:getAll:entry:getAll:ContentType:get:Asset:getAll get:Space:get", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [300, 450], "id": "b4ecfdaf-3dd8-4462-a893-00fcf0965acb"}, {"parameters": {"source": "previewApi", "operation": "getAll", "limit": 1, "additionalFields": {"content_type": "blogPost"}}, "name": "Contentful", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [510, 130], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "6f0e510a-f47b-406b-a3e4-50160a3837f8"}, {"parameters": {"source": "previewApi", "resource": "contentType", "contentTypeId": "blogPost", "additionalFields": {}}, "name": "Contentful1", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [510, 450], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "f667eb16-7c5e-42a8-b94a-465a0c17de0a"}, {"parameters": {"source": "previewApi", "resource": "asset", "limit": 1, "additionalFields": {}}, "name": "Contentful2", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [510, 300], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "9b9910ed-bcbb-4126-8625-d5599ff716a3"}, {"parameters": {"source": "previewApi", "resource": "asset", "operation": "get", "assetId": "5UvpPFo279OoVXHLRXqlo9"}, "name": "Contentful3", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [650, 300], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "disabled": true, "id": "0c26e50d-0829-41dc-8477-b93a812ff7cd"}, {"parameters": {"source": "previewApi", "entryId": "={{$node[\"Contentful\"].json[\"author\"][\"sys\"][\"id\"]}}"}, "name": "Contentful4", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [660, 130], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "disabled": true, "id": "c6c48d22-6a1f-4c2c-82eb-2c7c0a43d3e6"}, {"parameters": {"source": "previewApi", "resource": "locale", "limit": 1}, "name": "Contentful5", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [510, 750], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "0ff365a7-b88e-4bc2-965f-4198dc2287ca"}, {"parameters": {"source": "previewApi", "resource": "space"}, "name": "Contentful6", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [510, 610], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "43ab9aa7-dcef-416b-92fc-690040a35086"}], "connections": {"Contentful": {"main": [[{"node": "Contentful4", "type": "main", "index": 0}]]}, "Contentful2": {"main": [[{"node": "Contentful3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Contentful1", "type": "main", "index": 0}, {"node": "Contentful", "type": "main", "index": 0}, {"node": "Contentful5", "type": "main", "index": 0}, {"node": "Contentful2", "type": "main", "index": 0}, {"node": "Contentful6", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}