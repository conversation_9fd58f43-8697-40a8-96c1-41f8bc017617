{"createdAt": "2021-02-25T14:32:07.257Z", "updatedAt": "2021-11-12T13:03:17.136Z", "id": "72", "name": "Zulip:Message:sendPrivate update updateFile sendStream get delete:Stream:create getAll getSubscribed update delete:user:getAll get update", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 460], "id": "b29fb793-aa1e-4659-a887-a6444a072274"}, {"parameters": {"to": ["<EMAIL>"], "content": "=Message {{Date.now()}}"}, "name": "Zulip", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [430, 300], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "2eafb3ef-51ac-4153-ba65-f1c736be64f3"}, {"parameters": {"operation": "update", "messageId": "={{$node[\"Zulip\"].json[\"id\"]}}", "updateFields": {"content": "=Update content {{Date.now()}}"}}, "name": "Zulip1", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [590, 300], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "70a21ba0-3781-4598-86c8-24fa97d813b2"}, {"parameters": {"operation": "sendStream", "stream": 278954, "topic": "topic demonstration", "content": "Test Stream message {{Date.now()}}"}, "name": "Zulip2", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [740, 300], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "b631cfd9-4085-4a3f-9898-f3a2dd6053cc"}, {"parameters": {"operation": "get", "messageId": "={{$node[\"Zulip\"].json[\"id\"]}}"}, "name": "Zulip3", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [890, 300], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "bbfcd095-ab13-47d0-8826-cad588bb4269"}, {"parameters": {"operation": "delete", "messageId": "={{$node[\"Zulip\"].json[\"id\"]}}"}, "name": "Zulip4", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [1040, 300], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "af456d26-456e-4c8a-9df4-4dd5a9bd4f66"}, {"parameters": {"operation": "delete", "messageId": "={{$node[\"Zulip2\"].json[\"id\"]}}"}, "name": "Zulip5", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [1200, 300], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "8167982f-4c29-4767-9b2f-4c41d3c1d6da"}, {"parameters": {"operation": "updateFile"}, "name": "Zulip6", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [590, 470], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "27e360f0-add1-4478-b971-47cfaee55374"}, {"parameters": {"filePath": "/tmp/n8n-logo.png"}, "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [430, 470], "id": "ca173086-6491-4487-a201-8e7b6ec08c3a"}, {"parameters": {"resource": "stream", "subscriptions": {"properties": [{"name": "StreamTest", "description": "testing stream from n8n"}]}, "additionalFields": {}}, "name": "Zulip7", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [430, 620], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "53b92b41-c256-45d1-af57-73cdc927e4ad"}, {"parameters": {"resource": "stream", "operation": "getAll", "additionalFields": {"includeAllActive": false, "includeDefault": false, "includePublic": false}}, "name": "Zulip8", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [600, 620], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "95060b52-7fca-4fe6-89b7-3b95630d3876"}, {"parameters": {"resource": "stream", "operation": "update", "streamId": "={{$node[\"Function\"].json[\"stream_id\"]}}", "additionalFields": {"newName": "=UpdateStream{{Date.now()}}"}}, "name": "Zulip9", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [910, 620], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "afcf6e21-92b0-4b87-8de3-0fc7a309ab45"}, {"parameters": {"functionCode": "\nreturn [items[0]];"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 620], "notesInFlow": true, "notes": "Convert multiple result into one", "id": "281d2d4f-d8d9-4b9f-a150-f8650775d89f"}, {"parameters": {"resource": "stream", "operation": "getSubscribed", "additionalFields": {}}, "name": "Zulip10", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [590, 760], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "6193ce51-b480-44e4-ab58-4c4045432e7d"}, {"parameters": {"resource": "stream", "operation": "delete", "streamId": "={{$node[\"Function\"].json[\"stream_id\"]}}"}, "name": "Zulip11", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [1050, 620], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "8922c332-3948-4b92-a947-b06327a7cc95"}, {"parameters": {"resource": "user", "operation": "getAll", "additionalFields": {}}, "name": "Zulip12", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [440, 910], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "c2eb822b-4473-46ca-a70e-0ab4b7e59647"}, {"parameters": {"resource": "user", "operation": "get", "userId": "={{$node[\"Zulip12\"].json[\"user_id\"]}}", "additionalFields": {}}, "name": "Zulip13", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [590, 910], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "id": "77661e37-ad74-4274-9bdb-c809305369a5"}, {"parameters": {"resource": "user", "operation": "update", "userId": "={{$node[\"Zulip12\"].json[\"user_id\"]}}", "additionalFields": {"fullName": "mario"}}, "name": "Zulip14", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [750, 910], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "disabled": true, "id": "cb5c1ade-b500-48a9-a572-08b57316739f"}, {"parameters": {"resource": "user", "operation": "update", "userId": "={{$node[\"Zulip12\"].json[\"user_id\"]}}", "additionalFields": {"fullName": "nodeqa"}}, "name": "Zulip15", "type": "n8n-nodes-base.zulip", "typeVersion": 1, "position": [890, 910], "credentials": {"zulipApi": {"id": "60", "name": "Zulip creds"}}, "disabled": true, "id": "bd6174d3-edf6-438c-b60b-b18312458624"}], "connections": {"Zulip": {"main": [[{"node": "Zulip1", "type": "main", "index": 0}]]}, "Zulip1": {"main": [[{"node": "Zulip2", "type": "main", "index": 0}]]}, "Zulip2": {"main": [[{"node": "Zulip3", "type": "main", "index": 0}]]}, "Zulip3": {"main": [[{"node": "Zulip4", "type": "main", "index": 0}]]}, "Zulip4": {"main": [[{"node": "Zulip5", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "Zulip6", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}, {"node": "Zulip", "type": "main", "index": 0}, {"node": "Zulip7", "type": "main", "index": 0}, {"node": "Zulip12", "type": "main", "index": 0}]]}, "Zulip7": {"main": [[{"node": "Zulip8", "type": "main", "index": 0}, {"node": "Zulip10", "type": "main", "index": 0}]]}, "Zulip8": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Zulip9", "type": "main", "index": 0}]]}, "Zulip9": {"main": [[{"node": "Zulip11", "type": "main", "index": 0}]]}, "Zulip12": {"main": [[{"node": "Zulip13", "type": "main", "index": 0}]]}, "Zulip13": {"main": [[{"node": "Zulip14", "type": "main", "index": 0}]]}, "Zulip14": {"main": [[{"node": "Zulip15", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}