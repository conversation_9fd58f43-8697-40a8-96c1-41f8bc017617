{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344008708, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "ProfitWell": [{"startTime": 1747344008708, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 457, "executionStatus": "success", "data": {"main": [[{"json": {"id": "fd47e958-b607-46be-b5b1-2e87c43dffbf", "name": "n8nqa", "currency": "eur", "timezone": "Europe/Paris"}, "pairedItem": {"item": 0}}]]}}], "ProfitWell1": [{"startTime": 1747344009165, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 898, "executionStatus": "success", "data": {"main": [[{"json": {"recurring_revenue": 0, "date": "2025-05", "new_recurring_revenue": 0, "existing_recurring_revenue": 0, "churned_recurring_revenue": 0, "churned_recurring_revenue_cancellations": 0, "churned_recurring_revenue_delinquent": 0, "upgraded_recurring_revenue": 0, "downgraded_recurring_revenue": 0, "reactivated_recurring_revenue": 0, "active_customers": 0, "new_customers": 0, "existing_customers": 0, "churned_customers": 0, "churned_customers_cancellations": 0, "churned_customers_delinquent": 0, "upgraded_customers": 0, "downgraded_customers": 0, "reactivated_customers": 0, "average_revenue_per_user": 0, "lifetime_value": {"object": true}, "customers_churn_rate": {"object": true}, "customers_churn_cancellations_rate": {"object": true}, "customers_churn_delinquent_rate": {"object": true}, "revenue_churn_rate": {"object": true}, "revenue_churn_cancellations_rate": {"object": true}, "revenue_churn_delinquent_rate": {"object": true}, "revenue_retention_rate": {"object": true}, "customers_retention_rate": {"object": true}, "downgrade_rate": {"object": true}, "upgrade_rate": {"object": true}, "growth_rate": {"object": true}, "saas_quick_ratio": {"object": true}, "converted_recurring_revenue": 0, "new_trialing_customers": 0, "existing_trialing_customers": 1, "churned_trialing_customers": 0, "converted_customers": 0, "active_trialing_customers": 1, "customer_conversion_rate": 0, "trial_conversion_time": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "ProfitWell2": [{"startTime": 1747344010063, "executionIndex": 3, "source": [{"previousNode": "ProfitWell1"}], "hints": [], "executionTime": 880, "executionStatus": "success", "data": {"main": [[{"json": {"new_recurring_revenue": ["json array"], "upgraded_recurring_revenue": ["json array"], "downgraded_recurring_revenue": ["json array"], "churned_recurring_revenue": ["json array"], "reactivated_recurring_revenue": ["json array"], "new_customers": ["json array"], "upgraded_customers": ["json array"], "downgraded_customers": ["json array"], "churned_customers": ["json array"], "reactivated_customers": ["json array"], "recurring_revenue": ["json array"], "cumulative_net_new_mrr": ["json array"], "future_churn_mrr": ["json array"], "cumulative_new_trialing_customers": ["json array"], "active_customers": ["json array"]}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "ProfitWell2"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:08.708Z", "stoppedAt": "2025-05-15T21:20:10.943Z", "status": "running", "finished": true}