{"createdAt": "2021-05-06T09:34:45.652Z", "updatedAt": "2021-05-06T09:39:48.494Z", "id": "197", "name": "MQTT:sendMessage", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "850eb506-8c40-4257-92f9-13b39588d01a"}, {"parameters": {"topic": "TestTopic", "sendInputData": false, "message": "=MQTT-message{{Date.now()}}", "options": {"qos": 0}}, "name": "MQTT qos:1", "type": "n8n-nodes-base.mqtt", "typeVersion": 1, "position": [450, 160], "credentials": {"mqtt": {"id": "131", "name": "MQTT creds"}}, "id": "af2b9c5f-2b44-43d6-852f-436ebfc72c3c"}, {"parameters": {"topic": "TestTopic", "sendInputData": false, "message": "=MQTT-message{{Date.now()}}", "options": {"qos": 1}}, "name": "MQTT1 qos:0", "type": "n8n-nodes-base.mqtt", "typeVersion": 1, "position": [450, 310], "credentials": {"mqtt": {"id": "131", "name": "MQTT creds"}}, "id": "37d6e4f3-b06b-4bb3-8f68-ca54de4e41a9"}, {"parameters": {"topic": "TestTopic", "options": {"qos": 2}}, "name": "MQTT2 qos:2", "type": "n8n-nodes-base.mqtt", "typeVersion": 1, "position": [600, 450], "credentials": {"mqtt": {"id": "131", "name": "MQTT creds"}}, "id": "6e4693bb-23e5-4622-aa4f-2e93ce916f86"}, {"parameters": {"functionCode": "items = [\n  {\n    json:{\n      \"message\":`MQTT-item-message${Date.now()}`\n    }\n  }\n]\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [450, 450], "id": "37ef3107-c80c-4bad-a5d6-3d9a349ca76a"}], "connections": {"Start": {"main": [[{"node": "MQTT qos:1", "type": "main", "index": 0}, {"node": "MQTT1 qos:0", "type": "main", "index": 0}, {"node": "Function", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "MQTT2 qos:2", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}