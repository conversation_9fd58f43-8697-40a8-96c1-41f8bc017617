{"createdAt": "2021-02-25T13:38:22.940Z", "updatedAt": "2021-02-25T13:38:22.940Z", "id": "70", "name": "Contentful-delivery-api:locale:getAll:entry:getAll:ContentType:get:Asset:getAll get:Space:get", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [260, 390], "id": "92fcb1c3-e481-4350-910b-99de6956a87d"}, {"parameters": {"operation": "getAll", "limit": 1, "additionalFields": {"content_type": "blogPost"}}, "name": "Contentful", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [550, 250], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "e85d9486-96dc-4f89-9ab6-b0b91b1968df"}, {"parameters": {"resource": "contentType", "contentTypeId": "blogPost", "additionalFields": {}}, "name": "Contentful1", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [550, 420], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "4958d8ff-37dd-4086-a90b-fcf608a48d26"}, {"parameters": {"resource": "asset", "limit": 1, "additionalFields": {}}, "name": "Contentful2", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [550, 570], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "4dabb763-4379-4d65-b484-0dc528535896"}, {"parameters": {"resource": "asset", "operation": "get", "assetId": "5UvpPFo279OoVXHLRXqlo9"}, "name": "Contentful3", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [700, 570], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "disabled": true, "id": "cd08d703-328c-4f6e-a0e5-5681f2aa208a"}, {"parameters": {"entryId": "={{$node[\"Contentful\"].json[\"author\"][\"sys\"][\"id\"]}}"}, "name": "Contentful4", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [700, 250], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "disabled": true, "id": "f53c8f63-386c-491a-b5f9-119f27aaa421"}, {"parameters": {"resource": "locale", "limit": 1}, "name": "Contentful5", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [550, 100], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "429edf92-f2cf-4897-ba31-9413f57b0a46"}, {"parameters": {"resource": "space"}, "name": "Contentful6", "type": "n8n-nodes-base.contentful", "typeVersion": 1, "position": [550, 730], "credentials": {"contentfulApi": {"id": "47", "name": "Contenful creds"}}, "id": "36e76b3d-fb38-4abe-a4b9-81c8604f87f8"}], "connections": {"Contentful": {"main": [[{"node": "Contentful4", "type": "main", "index": 0}]]}, "Contentful2": {"main": [[{"node": "Contentful3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Contentful5", "type": "main", "index": 0}, {"node": "Contentful", "type": "main", "index": 0}, {"node": "Contentful1", "type": "main", "index": 0}, {"node": "Contentful2", "type": "main", "index": 0}, {"node": "Contentful6", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}