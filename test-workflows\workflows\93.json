{"createdAt": "2021-03-03T13:27:06.481Z", "updatedAt": "2021-03-03T13:32:50.258Z", "id": "93", "name": "Move Binary Data:toJSON:toBinary", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "490486f2-7ea9-410e-9281-84cd27836115"}, {"parameters": {"mode": "jsonToBinary", "options": {}}, "name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1, "position": [600, 300], "id": "9ddb3642-7086-4f67-8b20-9255bc2b99cc"}, {"parameters": {"functionCode": "item = {\n  name:'test',\n  indexes:[1,2,3],\n  subobj:{\n    name:'subtest'\n  }\n};\nreturn item;"}, "name": "FunctionItem", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [450, 300], "notesInFlow": true, "notes": "Set json data", "id": "8c99a563-2438-4822-b35d-3016c7524c50"}, {"parameters": {"options": {}}, "name": "Move Binary Data1", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1, "position": [750, 300], "id": "43d6faba-afc5-45bd-b477-a28054b75ddf"}, {"parameters": {"functionCode": "if(JSON.stringify($node['FunctionItem'].json)!==JSON.stringify($node['Move Binary Data1'].json)){\n  throw new Error('Problem in move binary node');\n}\n\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 300], "notesInFlow": true, "notes": "Evaluate the conversion result", "id": "e4fbc1b7-b88e-49c3-bb0c-9dda8a447617"}], "connections": {"Move Binary Data": {"main": [[{"node": "Move Binary Data1", "type": "main", "index": 0}]]}, "FunctionItem": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Move Binary Data1": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "FunctionItem", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}