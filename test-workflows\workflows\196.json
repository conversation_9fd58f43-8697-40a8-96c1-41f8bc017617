{"createdAt": "2021-05-04T09:29:11.382Z", "updatedAt": "2021-05-04T10:32:51.938Z", "id": "196", "name": "QuickBooks:Bill:create get update getAll delete:Item:getAll:get:Vendor:create get getAll update:Employee:create get update getAll:Customer:create get update getAll:Estimate:create get update getAll send delete:Invoice:create get update getAll send delete:Payment:create get update getAll send delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "e0188874-3b1d-4dfb-881d-74e79e543722"}, {"parameters": {"operation": "create", "displayName": "=Customer{{Date.now()}}", "additionalFields": {}}, "name": "QuickBooks", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [450, 390], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "689e8fe0-7b26-4a93-926e-221bd869ed8c"}, {"parameters": {"customerId": "={{$node[\"QuickBooks\"].json[\"Id\"]}}"}, "name": "QuickBooks1", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [600, 390], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "39187a1b-c5f9-4a8e-a60c-ae87771be5eb"}, {"parameters": {"operation": "update", "customerId": "={{$node[\"QuickBooks\"].json[\"Id\"]}}", "updateFields": {"Active": false}}, "name": "QuickBooks2", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [750, 390], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "4b0c15be-a7fa-440f-918a-3d197305640e"}, {"parameters": {"operation": "getAll", "filters": {}}, "name": "QuickBooks3", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [900, 390], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "9219c2cd-e3df-4afd-9a49-ca83c2b7b1ce"}, {"parameters": {"resource": "employee", "operation": "create", "FamilyName": "=Fname{{Date.now()}}", "GivenName": "=Name{{Date.now()}}", "additionalFields": {}}, "name": "QuickBooks4", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [450, 240], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "e66647c5-fdd7-4d10-b029-033cd7a71076"}, {"parameters": {"resource": "employee", "employeeId": "={{$node[\"QuickBooks4\"].json[\"Id\"]}}"}, "name": "QuickBooks5", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [600, 240], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "6d100355-e163-43ab-affa-850a3e547f0e"}, {"parameters": {"resource": "employee", "operation": "update", "employeeId": "={{$node[\"QuickBooks4\"].json[\"Id\"]}}", "updateFields": {"Active": false}}, "name": "QuickBooks6", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [750, 240], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "f53f7640-f512-4beb-beb1-61edbf24e164"}, {"parameters": {"resource": "employee", "operation": "getAll", "filters": {}}, "name": "QuickBooks7", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [900, 240], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "489b4d87-a2b1-4111-a165-b36762dc6de2"}, {"parameters": {"resource": "estimate", "operation": "create", "CustomerRef": "1", "Line": [{"itemId": "3", "DetailType": "SalesItemLineDetail", "Amount": 3, "Description": "test"}], "additionalFields": {}}, "name": "QuickBooks8", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [450, 540], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "f47dca6a-81eb-41ab-bf6f-34993f8333bc"}, {"parameters": {"resource": "estimate", "estimateId": "={{$node[\"QuickBooks8\"].json[\"Id\"]}}"}, "name": "QuickBooks9", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [600, 540], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "dc22140e-affa-4da1-9636-c0bd04987c39"}, {"parameters": {"resource": "estimate", "operation": "update", "estimateId": "={{$node[\"QuickBooks8\"].json[\"Id\"]}}", "updateFields": {"BillEmail": "<EMAIL>"}}, "name": "QuickBooks10", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [750, 540], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "bf4a83d1-dfbb-4a38-8140-fefe9ee06195"}, {"parameters": {"resource": "estimate", "operation": "getAll", "limit": 1, "filters": {}}, "name": "QuickBooks11", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [900, 540], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "8403016a-222f-4f05-80cf-2982bdeac426"}, {"parameters": {"resource": "estimate", "operation": "send", "estimateId": "={{$node[\"QuickBooks8\"].json[\"Id\"]}}", "email": "<EMAIL>"}, "name": "QuickBooks12", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [1050, 540], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "c00d2656-596b-4a09-ae82-09d5c71dcaab"}, {"parameters": {"resource": "estimate", "operation": "delete", "estimateId": "={{$node[\"QuickBooks8\"].json[\"Id\"]}}"}, "name": "QuickBooks13", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [1200, 540], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "0b7f754f-a7d9-49fe-b629-3c0cf1b1b7f5"}, {"parameters": {"resource": "invoice", "operation": "create", "CustomerRef": "5", "Line": [{"DetailType": "SalesItemLineDetail", "itemId": "7", "Amount": 0, "Description": "test"}], "additionalFields": {}}, "name": "QuickBooks14", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [450, 690], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "c5d80817-5c5a-4a78-bc72-4ca29b4757f4"}, {"parameters": {"resource": "invoice", "invoiceId": "={{$node[\"QuickBooks14\"].json[\"Id\"]}}"}, "name": "QuickBooks15", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [600, 690], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "6ad6231c-b645-4e00-b61f-ab4031b9f4f2"}, {"parameters": {"resource": "invoice", "operation": "getAll", "limit": 1, "filters": {}}, "name": "QuickBooks16", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [750, 690], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "88333b7b-f905-4c83-960e-3eb650f17d00"}, {"parameters": {"resource": "invoice", "operation": "update", "invoiceId": "={{$node[\"QuickBooks14\"].json[\"Id\"]}}", "updateFields": {"CustomerMemo": "test"}}, "name": "QuickBooks17", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [900, 690], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "e625f0d1-a0bb-4f8d-83b2-9dc62d3a526d"}, {"parameters": {"resource": "invoice", "operation": "send", "invoiceId": "={{$node[\"QuickBooks14\"].json[\"Id\"]}}", "email": "<EMAIL>"}, "name": "QuickBooks18", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [1050, 690], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "59851dc6-1aee-4c49-9cb1-3447df019be1"}, {"parameters": {"resource": "invoice", "operation": "delete", "invoiceId": "={{$node[\"QuickBooks14\"].json[\"Id\"]}}"}, "name": "QuickBooks19", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [1190, 690], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "68dda52d-9455-491e-b7b9-8dc10d6e8460"}, {"parameters": {"resource": "item", "itemId": "={{$node[\"QuickBooks21\"].json[\"Id\"]}}"}, "name": "QuickBooks22", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [600, -60], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "98c8ad9c-5053-4376-9dc0-95545e6f3d23"}, {"parameters": {"resource": "item", "operation": "getAll", "limit": 1, "filters": {}}, "name": "QuickBooks21", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [450, -60], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "e1b294da-dea1-4318-a487-390aa95c9287"}, {"parameters": {"resource": "payment", "operation": "create", "CustomerRef": "1", "TotalAmt": 50, "additionalFields": {}}, "name": "QuickBooks20", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [450, 840], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "aa58f15e-a22b-402c-8af3-39aeea6de874"}, {"parameters": {"resource": "payment", "paymentId": "={{$node[\"QuickBooks20\"].json[\"Id\"]}}"}, "name": "QuickBooks23", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [600, 840], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "f0226998-0489-4b64-ba0e-c06e13e75a9d"}, {"parameters": {"resource": "payment", "operation": "getAll", "limit": 1, "filters": {}}, "name": "QuickBooks24", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [750, 840], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "49554443-c484-4f7b-ad8f-8694e3163150"}, {"parameters": {"resource": "payment", "operation": "update", "paymentId": "={{$node[\"QuickBooks20\"].json[\"Id\"]}}", "updateFields": {"TxnDate": "2021-04-30T22:00:00.000Z"}}, "name": "QuickBooks25", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [900, 840], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "65fb3536-c40a-48a4-9e6a-ce4d4f24628a"}, {"parameters": {"resource": "payment", "operation": "send", "paymentId": "={{$node[\"QuickBooks20\"].json[\"Id\"]}}", "email": "<EMAIL>"}, "name": "QuickBooks26", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [1050, 840], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "33f3a73e-799e-4dc0-a8fc-5c9aa492a953"}, {"parameters": {"resource": "payment", "operation": "delete", "paymentId": "={{$node[\"QuickBooks20\"].json[\"Id\"]}}"}, "name": "QuickBooks27", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [1200, 840], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "fb87ee2d-7fad-4cbe-82a4-aede0b3462c1"}, {"parameters": {"resource": "vendor", "operation": "create", "displayName": "=Vendor{{Date.now()}}", "additionalFields": {}}, "name": "QuickBooks28", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [450, 90], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "09a16143-613f-4812-926a-acacbefb63d1"}, {"parameters": {"resource": "vendor", "vendorId": "={{$node[\"QuickBooks28\"].json[\"Id\"]}}"}, "name": "QuickBooks29", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [600, 90], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "ec53abeb-83ad-4cdb-b419-56eff8ed7de7"}, {"parameters": {"resource": "vendor", "operation": "getAll", "limit": 1, "filters": {}}, "name": "QuickBooks30", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [750, 90], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "f3c16a5c-83c6-410c-b382-1b22dd7404e1"}, {"parameters": {"resource": "vendor", "operation": "update", "vendorId": "={{$node[\"QuickBooks28\"].json[\"Id\"]}}", "updateFields": {"Active": false}}, "name": "QuickBooks31", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [900, 90], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "0bf8d78c-d02e-4b3c-a1fc-98a7afe4f7b9"}, {"parameters": {"resource": "bill", "operation": "create", "VendorRef": "46", "Line": [{"DetailType": "AccountBasedExpenseLineDetail", "Amount": 152, "Description": "test", "accountId": "78"}], "additionalFields": {}}, "name": "QuickBooks37", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [450, -210], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "2f7af95b-cbb8-4272-bdaf-64cd9906ae47"}, {"parameters": {"resource": "bill", "billId": "={{$node[\"QuickBooks37\"].json[\"Id\"]}}"}, "name": "QuickBooks38", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [600, -210], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "28dbf9b4-288b-4726-8796-718e9b586cca"}, {"parameters": {"resource": "bill", "operation": "update", "billId": "={{$node[\"QuickBooks37\"].json[\"Id\"]}}", "updateFields": {"DueDate": "2021-04-30T22:00:00.000Z"}}, "name": "QuickBooks39", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [750, -210], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "2843f656-3ee4-4e51-a376-315d92f8d9fd"}, {"parameters": {"resource": "bill", "operation": "getAll", "limit": 1, "filters": {}}, "name": "QuickBooks40", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [900, -210], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "624d0f96-d465-448b-b9a1-50483479e032"}, {"parameters": {"resource": "bill", "operation": "delete", "billId": "={{$node[\"QuickBooks37\"].json[\"Id\"]}}"}, "name": "QuickBooks41", "type": "n8n-nodes-base.quickbooks", "typeVersion": 1, "position": [1050, -210], "credentials": {"quickBooksOAuth2Api": {"id": "160", "name": "QuickBooks OAuth2 API creds"}}, "id": "0c89671b-1312-46b7-92af-4b879dbd5ff0"}], "connections": {"Start": {"main": [[{"node": "QuickBooks28", "type": "main", "index": 0}, {"node": "QuickBooks21", "type": "main", "index": 0}, {"node": "QuickBooks4", "type": "main", "index": 0}, {"node": "QuickBooks", "type": "main", "index": 0}, {"node": "QuickBooks37", "type": "main", "index": 0}, {"node": "QuickBooks8", "type": "main", "index": 0}, {"node": "QuickBooks14", "type": "main", "index": 0}, {"node": "QuickBooks20", "type": "main", "index": 0}]]}, "QuickBooks": {"main": [[{"node": "QuickBooks1", "type": "main", "index": 0}]]}, "QuickBooks1": {"main": [[{"node": "QuickBooks2", "type": "main", "index": 0}]]}, "QuickBooks2": {"main": [[{"node": "QuickBooks3", "type": "main", "index": 0}]]}, "QuickBooks4": {"main": [[{"node": "QuickBooks5", "type": "main", "index": 0}]]}, "QuickBooks5": {"main": [[{"node": "QuickBooks6", "type": "main", "index": 0}]]}, "QuickBooks6": {"main": [[{"node": "QuickBooks7", "type": "main", "index": 0}]]}, "QuickBooks8": {"main": [[{"node": "QuickBooks9", "type": "main", "index": 0}]]}, "QuickBooks9": {"main": [[{"node": "QuickBooks10", "type": "main", "index": 0}]]}, "QuickBooks10": {"main": [[{"node": "QuickBooks11", "type": "main", "index": 0}]]}, "QuickBooks11": {"main": [[{"node": "QuickBooks12", "type": "main", "index": 0}]]}, "QuickBooks12": {"main": [[{"node": "QuickBooks13", "type": "main", "index": 0}]]}, "QuickBooks14": {"main": [[{"node": "QuickBooks15", "type": "main", "index": 0}]]}, "QuickBooks15": {"main": [[{"node": "QuickBooks16", "type": "main", "index": 0}]]}, "QuickBooks16": {"main": [[{"node": "QuickBooks17", "type": "main", "index": 0}]]}, "QuickBooks17": {"main": [[{"node": "QuickBooks18", "type": "main", "index": 0}]]}, "QuickBooks18": {"main": [[{"node": "QuickBooks19", "type": "main", "index": 0}]]}, "QuickBooks21": {"main": [[{"node": "QuickBooks22", "type": "main", "index": 0}]]}, "QuickBooks20": {"main": [[{"node": "QuickBooks23", "type": "main", "index": 0}]]}, "QuickBooks23": {"main": [[{"node": "QuickBooks24", "type": "main", "index": 0}]]}, "QuickBooks24": {"main": [[{"node": "QuickBooks25", "type": "main", "index": 0}]]}, "QuickBooks25": {"main": [[{"node": "QuickBooks26", "type": "main", "index": 0}]]}, "QuickBooks26": {"main": [[{"node": "QuickBooks27", "type": "main", "index": 0}]]}, "QuickBooks28": {"main": [[{"node": "QuickBooks29", "type": "main", "index": 0}]]}, "QuickBooks29": {"main": [[{"node": "QuickBooks30", "type": "main", "index": 0}]]}, "QuickBooks30": {"main": [[{"node": "QuickBooks31", "type": "main", "index": 0}]]}, "QuickBooks37": {"main": [[{"node": "QuickBooks38", "type": "main", "index": 0}]]}, "QuickBooks38": {"main": [[{"node": "QuickBooks39", "type": "main", "index": 0}]]}, "QuickBooks39": {"main": [[{"node": "QuickBooks40", "type": "main", "index": 0}]]}, "QuickBooks40": {"main": [[{"node": "QuickBooks41", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}