{"createdAt": "2021-03-22T11:26:13.716Z", "updatedAt": "2021-03-24T08:32:29.467Z", "id": "143", "name": "Clearbit:Company:enrich autocomplete:Person:enrich", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [-60, 280], "id": "8033ed4d-ce87-413e-a813-6a86713e471d"}, {"parameters": {"domain": "n8n.io", "additionalFields": {"companyName": "n8n", "twitter": "n8n_io"}}, "name": "Clearbit", "type": "n8n-nodes-base.clearbit", "typeVersion": 1, "position": [150, 200], "credentials": {"clearbitApi": {"id": "114", "name": "Clearbit API creds"}}, "id": "09a11c3f-55a2-4d52-8040-5c19b0656758"}, {"parameters": {"operation": "autocomplete", "name": "n8n"}, "name": "Clearbit1", "type": "n8n-nodes-base.clearbit", "typeVersion": 1, "position": [350, 200], "credentials": {"clearbitApi": {"id": "114", "name": "Clearbit API creds"}}, "id": "e71587e3-ca16-4197-b0a7-d9c33f2c82b3"}, {"parameters": {"resource": "person", "email": "<EMAIL>", "additionalFields": {"company": "n8n"}}, "name": "Clearbit2", "type": "n8n-nodes-base.clearbit", "typeVersion": 1, "position": [150, 350], "credentials": {"clearbitApi": {"id": "114", "name": "Clearbit API creds"}}, "id": "f1c59ebb-3be0-48a5-bd6b-aed005fb54ea"}], "connections": {"Clearbit": {"main": [[{"node": "Clearbit1", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Clearbit2", "type": "main", "index": 0}, {"node": "Clearbit", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}