{"createdAt": "2021-03-19T09:20:03.334Z", "updatedAt": "2021-03-19T09:22:57.813Z", "id": "139", "name": "Shopify:Product:create update get getAll delete:Order:create update get getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "d841866b-3ccd-4f04-97ae-799cc649c163"}, {"parameters": {"resource": "product", "title": "=Product{{Date.now()}}", "additionalFields": {}}, "name": "Shopify", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [450, 300], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "84e29b73-0cc6-4793-a48b-8972fc733c16"}, {"parameters": {"resource": "product", "operation": "update", "productId": "={{$node[\"Shopify\"].json[\"id\"]}}", "updateFields": {"title": "=Updated{{$node[\"Shopify\"].json[\"title\"]}}"}}, "name": "Shopify1", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [1350, 300], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "4013bda3-f9d6-423a-a936-df538232f5e0"}, {"parameters": {"resource": "product", "operation": "get", "productId": "={{$node[\"Shopify\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Shopify2", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [1500, 300], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "bbc9b837-505c-4551-9fb1-4a9da938d9f9"}, {"parameters": {"resource": "product", "operation": "getAll", "limit": 1, "additionalFields": {}}, "name": "Shopify3", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [1650, 300], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "e0fffb77-cbd1-47df-8f86-ef2ef89fd6c7"}, {"parameters": {"resource": "product", "operation": "delete", "productId": "={{$node[\"Shopify\"].json[\"id\"]}}"}, "name": "Shopify4", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [1800, 300], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "61f48867-0cc6-4128-8b2b-ce4cbf60e524"}, {"parameters": {"additionalFields": {"email": "=fake{{Date.now()}}@gmail.com", "tags": "test,", "test": ""}, "limeItemsUi": {"lineItemValues": [{"productId": "={{$node[\"Shopify\"].json[\"id\"]}}", "title": "=Line{{Date.now()}}", "grams": "15", "price": "101"}]}}, "name": "Shopify5", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [600, 450], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "c1a9b348-cd93-46be-88e4-ccc2a2698fdb"}, {"parameters": {"operation": "update", "orderId": "={{$node[\"Shopify5\"].json[\"id\"]}}", "updateFields": {"email": "=updated{{$node[\"Shopify5\"].json[\"email\"]}}"}}, "name": "Shopify6", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [750, 450], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "e9fd5c83-7ed5-430e-b976-402027f53177"}, {"parameters": {"operation": "get", "orderId": "={{$node[\"Shopify5\"].json[\"id\"]}}", "options": {}}, "name": "Shopify7", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [900, 450], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "9ff0060c-8878-4893-84ae-a24deba7166d"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}}, "name": "Shopify8", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [1050, 450], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "5003db2b-06c5-42ca-899a-3f2e49aa0518"}, {"parameters": {"operation": "delete", "orderId": "={{$node[\"Shopify5\"].json[\"id\"]}}"}, "name": "Shopify9", "type": "n8n-nodes-base.shopify", "typeVersion": 1, "position": [1200, 450], "credentials": {"shopifyApi": {"id": "109", "name": "Shopify API creds"}}, "id": "c6677e77-ca3d-4d7f-b8e9-87d19786a845"}], "connections": {"Shopify": {"main": [[{"node": "Shopify5", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Shopify", "type": "main", "index": 0}]]}, "Shopify1": {"main": [[{"node": "Shopify2", "type": "main", "index": 0}]]}, "Shopify2": {"main": [[{"node": "Shopify3", "type": "main", "index": 0}]]}, "Shopify3": {"main": [[{"node": "Shopify4", "type": "main", "index": 0}]]}, "Shopify5": {"main": [[{"node": "Shopify6", "type": "main", "index": 0}]]}, "Shopify6": {"main": [[{"node": "Shopify7", "type": "main", "index": 0}]]}, "Shopify7": {"main": [[{"node": "Shopify8", "type": "main", "index": 0}]]}, "Shopify8": {"main": [[{"node": "Shopify9", "type": "main", "index": 0}]]}, "Shopify9": {"main": [[{"node": "Shopify1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}