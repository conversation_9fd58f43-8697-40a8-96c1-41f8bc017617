{"createdAt": "2021-05-12T10:06:38.839Z", "updatedAt": "2021-06-02T10:31:43.824Z", "id": "203", "name": "Paddle:Payment:getAll reschedule:Plan:getAll get:Product:getAll:Coupon:create update getAll:User:getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "6dca848e-e78f-4e1e-b637-c5b7a9b6d4cd"}, {"parameters": {"resource": "plan", "operation": "getAll", "limit": 1}, "name": "Paddle", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [450, 230], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "id": "552a93b0-4047-4ba5-8542-858e843045a5"}, {"parameters": {"resource": "plan", "planId": "={{$node[\"Paddle\"].json[\"id\"]}}"}, "name": "Paddle1", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [600, 230], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "id": "330027d9-fb24-4cd1-98bd-07b388ed3d0c"}, {"parameters": {"resource": "product", "limit": 1}, "name": "Paddle2", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [450, 380], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "id": "a88765a6-f48f-428e-a13d-94e3c711e1eb"}, {"parameters": {"resource": "user", "limit": 1, "additionalFields": {}}, "name": "Paddle3", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [450, 530], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "id": "a6911282-01de-4125-9479-b7ec3111edee"}, {"parameters": {"resource": "payment", "limit": 1, "additionalFields": {}}, "name": "Paddle4", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [450, 80], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "id": "53405b18-8d3a-42b3-9c28-fa0416a1c240"}, {"parameters": {"couponType": "product", "productIds": "={{$node[\"Paddle2\"].json[\"id\"]}}", "discountAmount": 50, "currency": "USD", "additionalFields": {}}, "name": "Paddle5", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [600, 430], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "id": "b2d6f9c0-6134-4723-b9b1-67b68e0cfb6f"}, {"parameters": {"operation": "update", "couponCode": "={{$node[\"Paddle5\"].json[\"coupon\"]}}", "additionalFields": {"discount": {"discountProperties": {"currency": "USD", "discountAmount": 5}}}}, "name": "Paddle6", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [750, 430], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "id": "9f0c228c-f1c8-47ca-9702-2e373cc0b632"}, {"parameters": {"operation": "getAll", "productId": "={{$node[\"Paddle2\"].json[\"id\"]}}", "returnAll": true}, "name": "Paddle7", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [900, 430], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "notes": "CAP_RESULTS_LENGTH=1", "id": "86d2cc5d-93f5-4aa5-803e-c5995c89be66"}, {"parameters": {"resource": "payment", "operation": "reschedule", "paymentId": "={{$node[\"Paddle4\"].json[\"id\"]}}", "date": "={{$node[\"Date & Time\"].json[\"data\"]}}"}, "name": "Paddle8", "type": "n8n-nodes-base.paddle", "typeVersion": 1, "position": [760, 80], "credentials": {"paddleApi": {"id": "173", "name": "Paddle API sandbox creds"}}, "id": "2b7076a8-2563-4808-970a-934e1389b517"}, {"parameters": {"action": "calculate", "value": "={{Date.now()}}", "duration": 5, "options": {}}, "name": "Date & Time", "type": "n8n-nodes-base.dateTime", "typeVersion": 1, "position": [600, 80], "id": "8282ff84-da5e-4da6-97d7-84c4d08e9947"}], "connections": {"Paddle": {"main": [[{"node": "Paddle1", "type": "main", "index": 0}]]}, "Paddle2": {"main": [[{"node": "Paddle5", "type": "main", "index": 0}]]}, "Paddle4": {"main": [[{"node": "Date & Time", "type": "main", "index": 0}]]}, "Paddle5": {"main": [[{"node": "Paddle6", "type": "main", "index": 0}]]}, "Paddle6": {"main": [[{"node": "Paddle7", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Paddle", "type": "main", "index": 0}, {"node": "Paddle2", "type": "main", "index": 0}, {"node": "Paddle3", "type": "main", "index": 0}, {"node": "Paddle4", "type": "main", "index": 0}]]}, "Date & Time": {"main": [[{"node": "Paddle8", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": {"node:Clockify Trigger": {"userId": "60335ad2f24e660123d7fdeb", "lastTimeChecked": "2021-05-12T08:09:47Z"}}, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}