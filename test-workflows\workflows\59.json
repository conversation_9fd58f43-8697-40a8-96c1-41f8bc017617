{"createdAt": "2021-02-23T10:02:06.947Z", "updatedAt": "2021-06-03T12:24:52.296Z", "id": "59", "name": "SendGrid:List:create update get getAll delete:Contact:upsert getAll get delete:mail:send", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [150, 400], "id": "cab42b4b-d902-4524-ba7c-a43e35900705"}, {"parameters": {"name": "=TestList{{Date.now()}}"}, "name": "SendGrid", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [350, 300], "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "79f6deca-f925-4162-8d81-14c466ace4ac"}, {"parameters": {"operation": "update", "listId": "={{$node[\"SendGrid\"].json[\"id\"]}}", "name": "=UpdatedTestList{{Date.now()}}"}, "name": "SendGrid1", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [500, 300], "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "80d58751-f59c-4685-b9a8-c8a438171803"}, {"parameters": {"operation": "get", "listId": "={{$node[\"SendGrid\"].json[\"id\"]}}"}, "name": "SendGrid2", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [650, 300], "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "5f09a27b-d2f9-4dd2-ad82-1ca7679c0499"}, {"parameters": {"operation": "delete", "listId": "={{$node[\"SendGrid\"].json[\"id\"]}}"}, "name": "SendGrid3", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [950, 300], "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "cfa7cafc-72ee-4197-916a-9fbe335eeecd"}, {"parameters": {"operation": "getAll", "limit": 1}, "name": "SendGrid4", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [800, 300], "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "2460a6da-9efa-4613-8083-58b940cefb38"}, {"parameters": {"resource": "contact", "email": "=fakeemail{{Date.now()}}@gmail.com", "additionalFields": {"firstName": "Fake", "lastName": "Email"}}, "name": "SendGrid5", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [350, 450], "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "0c1183f8-3849-4019-88cd-0e9498e772ce"}, {"parameters": {"resource": "contact", "operation": "getAll", "limit": 1, "filters": {}}, "name": "SendGrid6", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [650, 450], "executeOnce": false, "alwaysOutputData": true, "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "ae3988ef-4046-4a88-81a4-3f9dec1c90f3"}, {"parameters": {"resource": "contact", "operation": "get", "contactId": "={{$node[\"SendGrid6\"].json[\"id\"]}}"}, "name": "SendGrid7", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [800, 450], "alwaysOutputData": true, "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "727c4520-d781-4914-ae55-8413368dfe4a"}, {"parameters": {"resource": "contact", "operation": "delete", "ids": "=undefined"}, "name": "SendGrid8", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [950, 450], "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "99abe2ed-524c-482b-9a87-f5043c7568cd"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(1000);\nreturn item;"}, "name": "FunctionItem", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [500, 450], "notes": "Sleep function", "id": "5b5fc626-adc4-47af-811a-3785d04f8b75"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(5000);\n\n// Output data\nreturn items;"}, "name": "Sleep 5 Seconds", "type": "n8n-nodes-base.function", "position": [1400, 200], "typeVersion": 1, "id": "c1921fc7-275d-4fc9-bb8c-151d5a525b90"}, {"parameters": {"resource": "mail", "fromEmail": "<EMAIL>", "fromName": "node,qa", "toEmail": "<EMAIL>", "subject": "SendGrid test workflow", "contentValue": "=Message at {{(new Date).toUTCString()}}", "additionalFields": {}}, "name": "SendGrid9", "type": "n8n-nodes-base.sendGrid", "typeVersion": 1, "position": [350, 600], "credentials": {"sendGridApi": {"id": "46", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "189e1b95-2ad7-4c7e-8f87-b6114c4d99cb"}], "connections": {"SendGrid": {"main": [[{"node": "SendGrid1", "type": "main", "index": 0}]]}, "SendGrid1": {"main": [[{"node": "SendGrid2", "type": "main", "index": 0}]]}, "SendGrid2": {"main": [[{"node": "SendGrid4", "type": "main", "index": 0}]]}, "SendGrid4": {"main": [[{"node": "SendGrid3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "SendGrid", "type": "main", "index": 0}, {"node": "SendGrid5", "type": "main", "index": 0}, {"node": "SendGrid9", "type": "main", "index": 0}]]}, "SendGrid5": {"main": [[{"node": "FunctionItem", "type": "main", "index": 0}]]}, "SendGrid6": {"main": [[{"node": "SendGrid7", "type": "main", "index": 0}]]}, "SendGrid7": {"main": [[{"node": "SendGrid8", "type": "main", "index": 0}]]}, "FunctionItem": {"main": [[{"node": "SendGrid6", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}