{"createdAt": "2021-03-03T14:48:52.147Z", "updatedAt": "2021-03-03T14:49:01.728Z", "id": "96", "name": "MessageBird:Sms:send:Balance:get", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "a19a2b96-9d4e-40dc-ae19-ffa7d1de094c"}, {"parameters": {"resource": "balance"}, "name": "MessageBird", "type": "n8n-nodes-base.messageBird", "typeVersion": 1, "position": [500, 240], "credentials": {"messageBirdApi": {"id": "61", "name": "MessageBird creds"}}, "id": "ab3e0402-21cc-4699-8ba3-b995dd5f17bc"}, {"parameters": {"originator": "4930270504079 ", "recipients": "4930270504079 ", "message": "TestMessage", "additionalFields": {}}, "name": "MessageBird1", "type": "n8n-nodes-base.messageBird", "typeVersion": 1, "position": [500, 400], "credentials": {"messageBirdApi": {"id": "61", "name": "MessageBird creds"}}, "id": "67922f27-a0c3-49c9-a48f-98495a45e9e3"}], "connections": {"Start": {"main": [[{"node": "MessageBird", "type": "main", "index": 0}, {"node": "MessageBird1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}