{"createdAt": "2024-03-04T20:43:54.193Z", "updatedAt": "2024-03-04T20:43:57.000Z", "id": "234", "name": "BasicLLMChain:MistralChat", "active": false, "nodes": [{"parameters": {"model": "mistral-tiny", "options": {"temperature": 0}}, "id": "e825ced6-f1d5-47ab-8aa7-b6c6959732f9", "name": "Mistral Cloud Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "typeVersion": 1, "position": [1180, 720], "credentials": {"mistralCloudApi": {"id": "Rl7R5orXMm9FvxZS", "name": "Mistral Cloud account"}}}, {"parameters": {"promptType": "define", "text": "How much is 1+1? Only provide the numerical answer without any other text.\n"}, "id": "32c781c6-ce33-4ab5-bbd8-14651968daab", "name": "Mistral Cloud Chat", "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [1160, 560]}, {"parameters": {}, "id": "72dd7380-7254-4934-9562-f278a776dedd", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [980, 560]}], "connections": {"Mistral Cloud Chat Model": {"ai_languageModel": [[{"node": "Mistral Cloud Chat", "type": "ai_languageModel", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Mistral Cloud Chat", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "4bd59e87-bd2e-42ad-976d-6bf03535cf86", "triggerCount": 0, "tags": []}