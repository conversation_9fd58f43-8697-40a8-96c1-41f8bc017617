{"createdAt": "2021-02-25T15:49:55.037Z", "updatedAt": "2021-06-07T14:53:16.628Z", "id": "73", "name": "Youtube:Channel:get getAll update:Playlist:create update getAll get delete:PlaylistItem:add getAll get delete:videoCategory:getAll:Video:rate get upload update delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [310, 550], "id": "2ee04975-ff22-40e0-a3a9-d2542266c3bf"}, {"parameters": {"limit": 1, "filters": {"id": "UCkdph8FDLpq2UD2i_OlwErA"}, "options": {}}, "name": "YouTube", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [800, 300], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "8688bf50-6db1-432a-851e-fa4cb2f33439"}, {"parameters": {"operation": "get", "channelId": "UCiHVTkJtWSdc9N3h0nUGWLg"}, "name": "YouTube1", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [500, 300], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "18508d2b-7662-4eb0-bb2b-82eebcd49f11"}, {"parameters": {"operation": "update", "channelId": "={{$node[\"YouTube\"].json[\"id\"]}}", "updateFields": {"brandingSettingsUi": {"channelSettingsValues": {"channel": {"description": "=Update description {{Date.now()}}"}}}}}, "name": "YouTube2", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [950, 300], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "83fcbb4e-5ce7-470d-8b11-8f02964774f6"}, {"parameters": {"operation": "uploadBanner", "channelId": "UCkdph8FDLpq2UD2i_OlwErA"}, "name": "YouTube3", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1250, 300], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "disabled": true, "id": "94865deb-30fa-4250-8637-ce93c0bc12d7"}, {"parameters": {"resource": "playlist", "operation": "create", "title": "=Test{{Date.now()}}", "options": {}}, "name": "YouTube4", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [500, 450], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "ca4cb125-1083-4e44-a05a-76191b2c6dd4"}, {"parameters": {"resource": "playlist", "operation": "update", "playlistId": "={{$node[\"YouTube4\"].json[\"id\"]}}", "title": "=UpdatePlaylist{{Date.now()}}", "updateFields": {}}, "name": "YouTube5", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [800, 450], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "55cdf544-6908-4fc8-afad-2752f2373a14"}, {"parameters": {"resource": "playlist", "limit": 1, "filters": {}, "options": {}}, "name": "YouTube6", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1860, 450], "alwaysOutputData": true, "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "7275ae09-2a2b-4a79-8a11-6b37b51c3426"}, {"parameters": {"resource": "playlistItem", "playlistId": "={{$node[\"YouTube4\"].json[\"id\"]}}", "videoId": "sJO3b0WXm8I", "options": {}}, "name": "YouTube9", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [950, 550], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "ed7dcec9-c5d5-4627-a859-0475ab304c88"}, {"parameters": {"resource": "playlistItem", "operation": "getAll", "playlistId": "={{$node[\"YouTube4\"].json[\"id\"]}}", "part": ["id", "status", "contentDetails"], "limit": 1, "options": {}}, "name": "YouTube10", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1250, 550], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "c02c85b3-59e4-4fcf-ad40-1fd9cad8c2f7"}, {"parameters": {"resource": "playlistItem", "operation": "get", "playlistItemId": "={{$node[\"YouTube9\"].json[\"id\"]}}", "part": ["contentDetails", "id", "status"], "options": {}}, "name": "YouTube11", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1400, 550], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "13002ca6-7817-41c3-b1fe-0d078c8e82f9"}, {"parameters": {"resource": "playlistItem", "operation": "delete", "playlistItemId": "={{$node[\"YouTube9\"].json[\"id\"]}}", "options": {}}, "name": "YouTube12", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1700, 550], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "b0e8f742-b143-4eab-b227-482b3f772e3e"}, {"parameters": {"resource": "video", "operation": "rate", "videoId": "sJO3b0WXm8I", "rating": "like"}, "name": "YouTube13", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [500, 800], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "c13bc29b-3e16-4d0b-ba98-24156e1d64b0"}, {"parameters": {"resource": "video", "operation": "get", "videoId": "sJO3b0WXm8I", "part": ["status", "id", "snippet"], "options": {}}, "name": "YouTube14", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [800, 800], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "dd846141-f074-41a3-b9f0-4e32ec04639e"}, {"parameters": {"resource": "video", "operation": "upload", "title": "Earth spin", "regionCode": "DE", "categoryId": "27", "options": {}}, "name": "YouTube15", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1250, 800], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "disabled": true, "id": "4bd4cca1-e76c-4889-b5df-5d2eefe7d74c"}, {"parameters": {"url": "https://file-examples-com.github.io/uploads/2017/04/file_example_MP4_480_1_5MG.mp4", "responseFormat": "file", "options": {}}, "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [950, 800], "disabled": true, "id": "b78b84af-4759-42b8-812a-af46ab04d6fb"}, {"parameters": {"resource": "video", "operation": "update", "videoId": "={{$node[\"YouTube15\"].json[\"id\"]}}", "title": "=Updated Earth spin", "regionCode": "DE", "categoryId": "27", "updateFields": {}}, "name": "YouTube16", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1400, 800], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "disabled": true, "id": "cdb2885f-ab9a-41b1-8e84-071aa040e91a"}, {"parameters": {"resource": "video", "operation": "delete", "videoId": "={{$node[\"YouTube15\"].json[\"id\"]}}", "options": {}}, "name": "YouTube17", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1690, 800], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "disabled": true, "id": "15ebb1e6-b8f5-458c-803a-ddd62105b07c"}, {"parameters": {"resource": "videoCategory", "regionCode": "DE", "limit": 1}, "name": "YouTube18", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [500, 630], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "8ec4326f-f615-4acd-bedf-7d138051058a"}, {"parameters": {"filePath": "/tmp/n8n-logo.png"}, "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [1100, 300], "disabled": true, "id": "01e62fe7-5350-4def-9640-18171cbde303"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second", "type": "n8n-nodes-base.function", "position": [650, 300], "typeVersion": 1, "id": "bea9b966-028f-4891-892b-30415cb38452"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second1", "type": "n8n-nodes-base.function", "position": [650, 450], "typeVersion": 1, "id": "74460a7d-956c-4a9c-8357-de6b2d1e6076"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second2", "type": "n8n-nodes-base.function", "position": [1100, 550], "typeVersion": 1, "id": "a565a893-fdb1-45f5-96b9-846146a10c14"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second3", "type": "n8n-nodes-base.function", "position": [1550, 550], "typeVersion": 1, "id": "4659b2a6-7c8d-4290-9f57-b17e9c566c50"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second4", "type": "n8n-nodes-base.function", "position": [650, 800], "typeVersion": 1, "id": "15cca754-b296-4cb5-93d1-efd315175e9e"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second5", "type": "n8n-nodes-base.function", "position": [1110, 800], "typeVersion": 1, "disabled": true, "id": "caf5b31c-9e80-4796-8ded-c9e1401adbd8"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second6", "type": "n8n-nodes-base.function", "position": [1550, 800], "typeVersion": 1, "disabled": true, "id": "8b08f732-62ba-48d3-980b-30b225c6a029"}, {"parameters": {"resource": "playlist", "operation": "delete", "playlistId": "={{$node[\"YouTube4\"].json[\"id\"]}}", "options": {}}, "name": "YouTube7", "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [2030, 450], "credentials": {"youTubeOAuth2Api": {"id": "62", "name": "YouTube OAuth2 creds"}}, "id": "2b8b1d2f-5864-481d-a735-700380ccb2ea"}], "connections": {"YouTube": {"main": [[{"node": "YouTube2", "type": "main", "index": 0}]]}, "YouTube1": {"main": [[{"node": "Sleep 0.5 second", "type": "main", "index": 0}]]}, "YouTube4": {"main": [[{"node": "Sleep 0.5 second1", "type": "main", "index": 0}]]}, "YouTube5": {"main": [[{"node": "YouTube9", "type": "main", "index": 0}]]}, "YouTube6": {"main": [[{"node": "YouTube7", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "YouTube13", "type": "main", "index": 0}, {"node": "YouTube18", "type": "main", "index": 0}, {"node": "YouTube4", "type": "main", "index": 0}, {"node": "YouTube1", "type": "main", "index": 0}]]}, "YouTube9": {"main": [[{"node": "Sleep 0.5 second2", "type": "main", "index": 0}]]}, "YouTube10": {"main": [[{"node": "YouTube11", "type": "main", "index": 0}]]}, "YouTube11": {"main": [[{"node": "Sleep 0.5 second3", "type": "main", "index": 0}]]}, "YouTube12": {"main": [[{"node": "YouTube6", "type": "main", "index": 0}]]}, "YouTube13": {"main": [[{"node": "Sleep 0.5 second4", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Sleep 0.5 second5", "type": "main", "index": 0}]]}, "YouTube14": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "YouTube15": {"main": [[{"node": "YouTube16", "type": "main", "index": 0}]]}, "YouTube16": {"main": [[{"node": "Sleep 0.5 second6", "type": "main", "index": 0}]]}, "YouTube2": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "YouTube3", "type": "main", "index": 0}]]}, "Sleep 0.5 second": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "Sleep 0.5 second1": {"main": [[{"node": "YouTube5", "type": "main", "index": 0}]]}, "Sleep 0.5 second2": {"main": [[{"node": "YouTube10", "type": "main", "index": 0}]]}, "Sleep 0.5 second3": {"main": [[{"node": "YouTube12", "type": "main", "index": 0}]]}, "Sleep 0.5 second4": {"main": [[{"node": "YouTube14", "type": "main", "index": 0}]]}, "Sleep 0.5 second5": {"main": [[{"node": "YouTube15", "type": "main", "index": 0}]]}, "Sleep 0.5 second6": {"main": [[{"node": "YouTube17", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}