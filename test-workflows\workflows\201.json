{"createdAt": "2021-05-12T07:38:44.391Z", "updatedAt": "2021-06-04T17:24:19.887Z", "id": "201", "name": "Iterable:User:upsert get delete:UserList:add remove:Event:track", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "87df2205-aeb2-453f-920f-89f4d9da9608"}, {"parameters": {"values": {"string": [{"name": "userId", "value": "=User_{{Date.now()}}"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [430, 220], "id": "5d51609e-46fa-4c57-b1ed-447eb36ff334"}, {"parameters": {"identifier": "userId", "value": "={{$node[\"Set\"].json[\"userId\"]}}", "additionalFields": {"dataFieldsUi": {"dataFieldValues": []}}}, "name": "Iterable", "type": "n8n-nodes-base.iterable", "typeVersion": 1, "position": [590, 220], "credentials": {"iterableApi": {"id": "174", "name": "Iterable API creds"}}, "id": "11659a29-db59-49ad-a74c-02702ce391d1"}, {"parameters": {"operation": "get", "by": "userId", "userId": "={{$node[\"Set\"].json[\"userId\"]}}"}, "name": "Iterable1", "type": "n8n-nodes-base.iterable", "typeVersion": 1, "position": [900, 220], "credentials": {"iterableApi": {"id": "174", "name": "Iterable API creds"}}, "notes": "IGNORED_PROPERTIES=dataFields", "id": "d8af6ed5-9797-4904-880d-d3a68abb7457"}, {"parameters": {"resource": "userList", "listId": 798932, "identifier": "userId", "value": "={{$node[\"Set\"].json[\"userId\"]}}"}, "name": "Iterable2", "type": "n8n-nodes-base.iterable", "typeVersion": 1, "position": [1050, 320], "credentials": {"iterableApi": {"id": "174", "name": "Iterable API creds"}}, "id": "d5799965-0693-4d2e-bfd7-450d0022e355"}, {"parameters": {"resource": "userList", "operation": "remove", "listId": 798932, "identifier": "userId", "value": "={{$node[\"Set\"].json[\"userId\"]}}", "additionalFields": {}}, "name": "Iterable3", "type": "n8n-nodes-base.iterable", "typeVersion": 1, "position": [1200, 320], "credentials": {"iterableApi": {"id": "174", "name": "Iterable API creds"}}, "id": "d66b3b48-e631-47e0-ade1-fe4b6137623b"}, {"parameters": {"resource": "event", "name": "TestEvent", "additionalFields": {"id": "={{$node[\"Set\"].json[\"userId\"]}}", "userId": "={{$node[\"Set\"].json[\"userId\"]}}"}}, "name": "Iterable4", "type": "n8n-nodes-base.iterable", "typeVersion": 1, "position": [1350, 390], "credentials": {"iterableApi": {"id": "174", "name": "Iterable API creds"}}, "id": "73e5dc94-d847-4ade-95ef-98ea69237245"}, {"parameters": {"operation": "delete", "email": "={{$node[\"Iterable1\"].json[\"email\"]}}"}, "name": "Iterable5", "type": "n8n-nodes-base.iterable", "typeVersion": 1, "position": [1500, 210], "credentials": {"iterableApi": {"id": "174", "name": "Iterable API creds"}}, "id": "650fa586-231c-4544-bb92-772ac561f0ee"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second", "type": "n8n-nodes-base.function", "position": [740, 220], "typeVersion": 1, "id": "a4cdbbb5-a101-40f3-9f52-efe768d27e62"}], "connections": {"Set": {"main": [[{"node": "Iterable", "type": "main", "index": 0}]]}, "Iterable": {"main": [[{"node": "Sleep 0.5 second", "type": "main", "index": 0}]]}, "Iterable1": {"main": [[{"node": "Iterable2", "type": "main", "index": 0}]]}, "Iterable2": {"main": [[{"node": "Iterable3", "type": "main", "index": 0}]]}, "Iterable3": {"main": [[{"node": "Iterable4", "type": "main", "index": 0}]]}, "Iterable4": {"main": [[{"node": "Iterable5", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Sleep 0.5 second": {"main": [[{"node": "Iterable1", "type": "main", "index": 0}]]}}, "settings": {"timezone": "Europe/Berlin", "saveExecutionProgress": "DEFAULT"}, "staticData": {"node:Clockify Trigger": {"userId": "60335ad2f24e660123d7fdeb", "lastTimeChecked": "2021-05-12T10:25:03Z"}}, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}