{"data": {"startData": {}, "resultData": {"runData": {"When clicking ‘Test workflow’": [{"startTime": 1747344001481, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "OpenAI Chat Model": [{"startTime": 1747344001486, "executionTime": 660, "executionIndex": 2, "executionStatus": "success", "source": [{"previousNode": "AI Agent", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}, "tokenUsage": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["System: You are a helpful assistant\nHuman: What happened yesterday?"], "estimatedTokens": 14, "options": {"openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "model": "gpt-4o-mini", "timeout": 60000, "max_retries": 2, "configuration": {"baseURL": "https://api.openai.com/v1"}, "model_kwargs": {}}}}]]}, "metadata": {"subRun": [{"node": "OpenAI Chat Model", "runIndex": 0}, {"node": "OpenAI Chat Model", "runIndex": 1}]}}, {"startTime": 1747344002730, "executionTime": 667, "executionIndex": 7, "executionStatus": "success", "source": [{"previousNode": "AI Agent", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}, "tokenUsage": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["System: You are a helpful assistant\nHuman: What happened yesterday?\nAI: \nTool: "], "estimatedTokens": 20, "options": {"openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "model": "gpt-4o-mini", "timeout": 60000, "max_retries": 2, "configuration": {"baseURL": "https://api.openai.com/v1"}, "model_kwargs": {}}}}]]}}], "Vector Store Tool": [{"startTime": 1747344002162, "executionTime": 567, "executionIndex": 3, "executionStatus": "error", "source": [{"previousNode": "AI Agent", "previousNodeRun": 0}], "data": {"ai_tool": [[{"json": {"query": "What happened on October 23, 2023?"}}]]}, "inputOverride": {"ai_tool": [[{"json": {"query": "What happened on October 23, 2023?"}}]]}, "metadata": {"subRun": [{"node": "Vector Store Tool", "runIndex": 0}]}, "error": {"message": "The resource you are requesting could not be found", "timestamp": 1747344002729, "name": "NodeOperationError", "description": "The resource you are requesting could not be found", "context": {}, "cause": {"message": "The resource you are requesting could not be found", "timestamp": 1747344002729, "name": "NodeApiError", "description": "The model `gpt-4o-mini123` does not exist or you do not have access to it.", "context": {}, "cause": {"status": 404, "headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9405ad506891b881-DUB", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json; charset=utf-8", "date": "Thu, 15 May 2025 21:20:02 GMT", "server": "cloudflare", "set-cookie": "__cf_bm=_3t8lA2EICfoAVnY8jS2pZ8vSSfa8N26DkIApllAMH8-1747344002-*******-FLg_gCTgMgTTRm3_JoXq9N_TPKW6rr7xWPcCoC_Pa6DtV4BDsZhreK9e7Csam5QfoKEZPRGyDdeLdO7Qow6ZW9hI3ticwUSjDNTQqgwhNKU; path=/; expires=Thu, 15-May-25 21:50:02 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=974ecBFWT36IeNWLLAlnKDyMYU2zpqoQnqHSdftSXQE-1747344002766-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin", "x-content-type-options": "nosniff", "x-request-id": "req_f04a3a26fad606a682c63896539732e7"}, "request_id": "req_f04a3a26fad606a682c63896539732e7", "error": {"message": "The model `gpt-4o-mini123` does not exist or you do not have access to it.", "type": "invalid_request_error", "param": null, "code": "model_not_found"}, "code": "model_not_found", "param": null, "type": "invalid_request_error", "lc_error_code": "MODEL_NOT_FOUND", "attemptNumber": 1, "retriesLeft": 2}}}}], "In-Memory Vector Store": [{"startTime": 1747344002162, "executionTime": 406, "executionIndex": 4, "executionStatus": "success", "source": [{"previousNode": "Vector Store Tool", "previousNodeRun": 0}], "data": {"ai_vectorStore": [[{"json": {"response": ["json array"]}}]]}, "inputOverride": {"ai_vectorStore": [[{"json": {"query": "What happened on October 23, 2023?", "k": 4}}]]}, "metadata": {"subRun": [{"node": "In-Memory Vector Store", "runIndex": 0}]}}], "Embeddings OpenAI": [{"startTime": 1747344002162, "executionTime": 406, "executionIndex": 5, "executionStatus": "success", "source": [{"previousNode": "In-Memory Vector Store", "previousNodeRun": 0}], "data": {"ai_embedding": [[{"json": {"response": ["json array"]}}]]}, "inputOverride": {"ai_embedding": [[{"json": {"query": "What happened on October 23, 2023?"}}]]}, "metadata": {"subRun": [{"node": "Embeddings OpenAI", "runIndex": 0}]}}], "OpenAI Chat Model1": [{"startTime": 1747344002569, "executionTime": 160, "executionIndex": 6, "executionStatus": "error", "source": [{"previousNode": "Vector Store Tool", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"messages": ["json array"], "estimatedTokens": 52, "options": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["json array"], "estimatedTokens": 52, "options": {"object": true}}}]]}, "metadata": {"subRun": [{"node": "OpenAI Chat Model1", "runIndex": 0}]}, "error": {"message": "The resource you are requesting could not be found", "timestamp": 1747344002729, "name": "NodeApiError", "description": "The model `gpt-4o-mini123` does not exist or you do not have access to it.", "context": {}, "cause": {"status": 404, "headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9405ad506891b881-DUB", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json; charset=utf-8", "date": "Thu, 15 May 2025 21:20:02 GMT", "server": "cloudflare", "set-cookie": "__cf_bm=_3t8lA2EICfoAVnY8jS2pZ8vSSfa8N26DkIApllAMH8-1747344002-*******-FLg_gCTgMgTTRm3_JoXq9N_TPKW6rr7xWPcCoC_Pa6DtV4BDsZhreK9e7Csam5QfoKEZPRGyDdeLdO7Qow6ZW9hI3ticwUSjDNTQqgwhNKU; path=/; expires=Thu, 15-May-25 21:50:02 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=974ecBFWT36IeNWLLAlnKDyMYU2zpqoQnqHSdftSXQE-1747344002766-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin", "x-content-type-options": "nosniff", "x-request-id": "req_f04a3a26fad606a682c63896539732e7"}, "request_id": "req_f04a3a26fad606a682c63896539732e7", "error": {"message": "The model `gpt-4o-mini123` does not exist or you do not have access to it.", "type": "invalid_request_error", "param": null, "code": "model_not_found"}, "code": "model_not_found", "param": null, "type": "invalid_request_error", "lc_error_code": "MODEL_NOT_FOUND", "attemptNumber": 1, "retriesLeft": 2}}}], "AI Agent": [{"startTime": 1747344001481, "executionIndex": 1, "source": [{"previousNode": "When clicking ‘Test workflow’"}], "hints": [], "executionTime": 1919, "executionStatus": "success", "data": {"main": [[{"json": {"output": "I'm unable to retrieve the information about what happened yesterday. Would you like to ask about a specific event or topic?"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "AI Agent"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {"OpenAI Chat Model": [{"subRun": [{"node": "OpenAI Chat Model", "runIndex": 0}, {"node": "OpenAI Chat Model", "runIndex": 1}]}], "Embeddings OpenAI": [{"subRun": [{"node": "Embeddings OpenAI", "runIndex": 0}]}], "In-Memory Vector Store": [{"subRun": [{"node": "In-Memory Vector Store", "runIndex": 0}]}], "OpenAI Chat Model1": [{"subRun": [{"node": "OpenAI Chat Model1", "runIndex": 0}]}], "Vector Store Tool": [{"subRun": [{"node": "Vector Store Tool", "runIndex": 0}]}]}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:01.481Z", "stoppedAt": "2025-05-15T21:20:03.400Z", "status": "running", "finished": true}