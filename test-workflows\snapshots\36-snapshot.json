{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344002707, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Slack10": [{"startTime": 1747344002707, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 466, "executionStatus": "success", "data": {"main": [[{"json": {"id": "F08SQHFSW92", "created": 1747344002, "timestamp": 1747344002, "name": "-.txt", "title": "Untitled", "mimetype": "text/plain", "filetype": "text", "pretty_type": "Plain Text", "user": "U01NE1E8FFU", "user_team": "T01MZ82R8NB", "editable": true, "size": 72, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T01MZ82R8NB-F08SQHFSW92/-.txt", "url_private_download": "https://files.slack.com/files-pri/T01MZ82R8NB-F08SQHFSW92/download/-.txt", "permalink": "https://n8n-qa.slack.com/files/U01NE1E8FFU/F08SQHFSW92/-.txt", "permalink_public": "https://slack-files.com/T01MZ82R8NB-F08SQHFSW92-b62bcefe91", "edit_link": "https://n8n-qa.slack.com/files/U01NE1E8FFU/F08SQHFSW92/-.txt/edit", "preview": "Test file upload Thu May 15 2025 22:20:02 GMT+0100 (Irish Standard Time)", "preview_highlight": "<div class=\"CodeMirror cm-s-default CodeMirrorServer\">\n<div class=\"CodeMirror-code\">\n<div><pre>Test file upload Thu May 15 2025 22:20:02 GMT+0100 (Irish Standard Time)</pre></div>\n</div>\n</div>\n", "lines": 1, "lines_more": 0, "preview_is_truncated": false, "comments_count": 0, "is_starred": false, "shares": {"object": true}, "channels": ["json array"], "groups": ["json array"], "ims": ["json array"], "has_more_shares": false, "has_rich_preview": false, "file_access": "visible"}, "pairedItem": {"item": 0}}]]}}], "Slack2": [{"startTime": 1747344003173, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 584, "executionStatus": "success", "data": {"main": [[{"json": {"id": "C08SK5EM3HT", "name": "testchannelzpixicyzv", "is_channel": true, "is_group": false, "is_im": false, "is_mpim": false, "is_private": false, "created": 1747344003, "is_archived": false, "is_general": false, "unlinked": 0, "name_normalized": "testchannelzpixicyzv", "is_shared": false, "is_org_shared": false, "is_pending_ext_shared": false, "pending_shared": ["json array"], "context_team_id": "T01MZ82R8NB", "updated": 1747344003395, "parent_conversation": {"object": true}, "creator": "U01NE1E8FFU", "is_ext_shared": false, "shared_team_ids": ["json array"], "pending_connected_team_ids": ["json array"], "is_member": true, "last_read": "0000000000.000000", "topic": {"object": true}, "purpose": {"object": true}, "previous_names": ["json array"], "priority": 0}, "pairedItem": {"item": 0}}]]}}], "Slack24": [{"startTime": 1747344003757, "executionIndex": 3, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 133, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Slack11": [{"startTime": 1747344003890, "executionIndex": 4, "source": [{"previousNode": "Slack10"}], "hints": [], "executionTime": 164, "executionStatus": "success", "data": {"main": [[]]}}], "Slack9": [{"startTime": 1747344004055, "executionIndex": 5, "source": [{"previousNode": "Slack2"}], "hints": [], "executionTime": 562, "executionStatus": "success", "data": {"main": [[{"json": {"id": "C08SK5EM3HT", "name": "renamedtestchannelzpixicyzv", "is_channel": true, "is_group": false, "is_im": false, "is_mpim": false, "is_private": false, "created": 1747344003, "is_archived": false, "is_general": false, "unlinked": 0, "name_normalized": "renamedtestchannelzpixicyzv", "is_shared": false, "is_org_shared": false, "is_pending_ext_shared": false, "pending_shared": ["json array"], "context_team_id": "T01MZ82R8NB", "updated": 1747344004305, "parent_conversation": {"object": true}, "creator": "U01NE1E8FFU", "is_ext_shared": false, "shared_team_ids": ["json array"], "pending_connected_team_ids": ["json array"], "is_member": true, "last_read": "0000000000.000000", "topic": {"object": true}, "purpose": {"object": true}, "previous_names": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Slack21": [{"startTime": 1747344004617, "executionIndex": 6, "source": [{"previousNode": "Slack24"}], "hints": [], "executionTime": 220, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true}, "pairedItem": {"item": 0}}]]}}], "Slack3": [{"startTime": 1747344004837, "executionIndex": 7, "source": [{"previousNode": "Slack9"}], "hints": [], "executionTime": 189, "executionStatus": "success", "data": {"main": [[{"json": {"id": "C08SK5EM3HT", "name": "renamedtestchannelzpixicyzv", "is_channel": true, "is_group": false, "is_im": false, "is_mpim": false, "is_private": false, "created": 1747344003, "is_archived": false, "is_general": false, "unlinked": 0, "name_normalized": "renamedtestchannelzpixicyzv", "is_shared": false, "is_org_shared": false, "is_pending_ext_shared": false, "pending_shared": ["json array"], "context_team_id": "T01MZ82R8NB", "updated": 1747344004977, "parent_conversation": {"object": true}, "creator": "U01NE1E8FFU", "is_ext_shared": false, "shared_team_ids": ["json array"], "pending_connected_team_ids": ["json array"], "is_member": false, "topic": {"object": true}, "purpose": {"object": true}, "previous_names": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Slack22": [{"startTime": 1747344005026, "executionIndex": 8, "source": [{"previousNode": "Slack21"}], "hints": [], "executionTime": 131, "executionStatus": "success", "data": {"main": [[{"json": {"type": "channel", "channel": "C01MZ82T9TR", "date_create": 1747344004}, "pairedItem": {"item": 0}}]]}}], "Slack4": [{"startTime": 1747344005157, "executionIndex": 9, "source": [{"previousNode": "Slack3"}], "hints": [], "executionTime": 268, "executionStatus": "success", "data": {"main": [[{"json": {"id": "C08SK5EM3HT", "name": "renamedtestchannelzpixicyzv", "is_channel": true, "is_group": false, "is_im": false, "is_mpim": false, "is_private": false, "created": 1747344003, "is_archived": false, "is_general": false, "unlinked": 0, "name_normalized": "renamedtestchannelzpixicyzv", "is_shared": false, "is_org_shared": false, "is_pending_ext_shared": false, "pending_shared": ["json array"], "context_team_id": "T01MZ82R8NB", "updated": 1747344005369, "parent_conversation": {"object": true}, "creator": "U01NE1E8FFU", "is_ext_shared": false, "shared_team_ids": ["json array"], "pending_connected_team_ids": ["json array"], "is_member": false, "topic": {"object": true}, "purpose": {"object": true}, "previous_names": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Slack23": [{"startTime": 1747344005425, "executionIndex": 10, "source": [{"previousNode": "Slack22"}], "hints": [], "executionTime": 257, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true}, "pairedItem": {"item": 0}}]]}}], "Slack5": [{"startTime": 1747344005682, "executionIndex": 11, "source": [{"previousNode": "Slack4"}], "hints": [], "executionTime": 206, "executionStatus": "success", "data": {"main": [[{"json": {"id": "C08SK5EM3HT", "name": "renamedtestchannelzpixicyzv", "is_channel": true, "is_group": false, "is_im": false, "is_mpim": false, "is_private": false, "created": 1747344003, "is_archived": false, "is_general": false, "unlinked": 0, "name_normalized": "renamedtestchannelzpixicyzv", "is_shared": false, "is_org_shared": false, "is_pending_ext_shared": false, "pending_shared": ["json array"], "context_team_id": "T01MZ82R8NB", "updated": 1747344005369, "parent_conversation": {"object": true}, "creator": "U01NE1E8FFU", "is_ext_shared": false, "shared_team_ids": ["json array"], "pending_connected_team_ids": ["json array"], "is_member": true, "last_read": "0000000000.000000", "topic": {"object": true}, "purpose": {"object": true}, "previous_names": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Slack13": [{"startTime": 1747344005888, "executionIndex": 12, "source": [{"previousNode": "Slack5"}], "hints": [], "executionTime": 267, "executionStatus": "success", "data": {"main": [[{"json": {"id": "C08SK5EM3HT", "name": "renamedtestchannelzpixicyzv", "is_channel": true, "is_group": false, "is_im": false, "is_mpim": false, "is_private": false, "created": 1747344003, "is_archived": false, "is_general": false, "unlinked": 0, "name_normalized": "renamedtestchannelzpixicyzv", "is_shared": false, "is_org_shared": false, "is_pending_ext_shared": false, "pending_shared": ["json array"], "context_team_id": "T01MZ82R8NB", "updated": 1747344005369, "parent_conversation": {"object": true}, "creator": "U01NE1E8FFU", "is_ext_shared": false, "shared_team_ids": ["json array"], "pending_connected_team_ids": ["json array"], "is_member": true, "last_read": "0000000000.000000", "topic": {"object": true}, "purpose": {"object": true}, "previous_names": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Slack17": [{"startTime": 1747344006155, "executionIndex": 13, "source": [{"previousNode": "Slack13"}], "hints": [], "executionTime": 268, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true}, "pairedItem": {"item": 0}}]]}}], "Slack16": [{"startTime": 1747344006423, "executionIndex": 14, "source": [{"previousNode": "Slack17"}], "hints": [], "executionTime": 497, "executionStatus": "success", "data": {"main": [[{"json": {"id": "C08SK5EM3HT", "name": "renamedtestchannelzpixicyzv", "is_channel": true, "is_group": false, "is_im": false, "is_mpim": false, "is_private": false, "created": 1747344003, "is_archived": false, "is_general": false, "unlinked": 0, "name_normalized": "renamedtestchannelzpixicyzv", "is_shared": false, "is_org_shared": false, "is_pending_ext_shared": false, "pending_shared": ["json array"], "context_team_id": "T01MZ82R8NB", "updated": 1747344005369, "parent_conversation": {"object": true}, "creator": "U01NE1E8FFU", "is_ext_shared": false, "shared_team_ids": ["json array"], "pending_connected_team_ids": ["json array"], "is_member": true, "last_read": "1747344005.401359", "topic": {"object": true}, "purpose": {"object": true}, "previous_names": ["json array"], "priority": 0}, "pairedItem": {"item": 0}}]]}}], "Slack6": [{"startTime": 1747344006920, "executionIndex": 15, "source": [{"previousNode": "Slack16"}], "hints": [], "executionTime": 216, "executionStatus": "success", "data": {"main": [[{"json": {"id": "C01MZ82T9TR", "name": "random", "is_channel": true, "is_group": false, "is_im": false, "is_mpim": false, "is_private": false, "created": 1613575358, "is_archived": false, "is_general": false, "unlinked": 0, "name_normalized": "random", "is_shared": false, "is_org_shared": false, "is_pending_ext_shared": false, "pending_shared": ["json array"], "context_team_id": "T01MZ82R8NB", "updated": 1746595136190, "parent_conversation": {"object": true}, "creator": "U01NE1E8FFU", "is_ext_shared": false, "shared_team_ids": ["json array"], "pending_connected_team_ids": ["json array"], "is_member": true, "topic": {"object": true}, "purpose": {"object": true}, "properties": {"object": true}, "previous_names": ["json array"], "num_members": 2}, "pairedItem": {"item": 0}}]]}}], "Slack7": [{"startTime": 1747344007136, "executionIndex": 16, "source": [{"previousNode": "Slack6"}], "hints": [], "executionTime": 207, "executionStatus": "success", "data": {"main": [[{"json": {"subtype": "channel_join", "user": "U01NE1E8FFU", "text": "<@U01NE1E8FFU> has joined the channel", "type": "message", "ts": "1747344006.709799"}, "pairedItem": {"item": 0}}]]}}], "Slack18": [{"startTime": 1747344007343, "executionIndex": 17, "source": [{"previousNode": "Slack7"}], "hints": [], "executionTime": 150, "executionStatus": "success", "data": {"main": [[{"json": {"subtype": "channel_join", "user": "U01NE1E8FFU", "text": "<@U01NE1E8FFU> has joined the channel", "type": "message", "ts": "1747344006.709799"}, "pairedItem": {"item": 0}}]]}}], "Slack8": [{"startTime": 1747344007493, "executionIndex": 18, "source": [{"previousNode": "Slack18"}], "hints": [], "executionTime": 195, "executionStatus": "success", "data": {"main": [[{"json": {"member": "U01N08LEY9M"}, "pairedItem": {"item": 0}}]]}}], "Slack": [{"startTime": 1747344007688, "executionIndex": 19, "source": [{"previousNode": "Slack8"}], "hints": [], "executionTime": 279, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true}, "pairedItem": {"item": 0}}]]}}], "Slack14": [{"startTime": 1747344007967, "executionIndex": 20, "source": [{"previousNode": "<PERSON><PERSON>ck"}], "hints": [], "executionTime": 439, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true}, "pairedItem": {"item": 0}}]]}}], "Slack1": [{"startTime": 1747344008406, "executionIndex": 21, "source": [{"previousNode": "Slack14"}], "hints": [], "executionTime": 283, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Slack1"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:02.706Z", "stoppedAt": "2025-05-15T21:20:08.689Z", "status": "running", "finished": true}