{"createdAt": "2021-06-25T10:58:54.559Z", "updatedAt": "2021-07-09T10:01:28.338Z", "id": "221", "name": "Copper:Company:*:Person:*:Opportunity:*:Project:*:Task:*:CustomerSource:getAll:User:getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "3524ea35-2975-49aa-8a25-0980af2d16ff"}, {"parameters": {"name": "=Company{{Date.now()}}", "additionalFields": {"address": {"addressFields": {"street": "street", "city": "city", "state": "state", "postal_code": "1001", "country": "country"}}, "details": "Description ", "email_domain": "=Test{{Date.now()}}", "phone_numbers": {"phoneFields": [{"number": "42424242", "category": "mobile"}]}}}, "name": "Copper", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [500, 130], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "c7d44454-2d3f-446a-a21f-ba97dee874c2"}, {"parameters": {"operation": "get", "companyId": "={{$node[\"Copper\"].json[\"id\"]}}"}, "name": "Copper1", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [650, 130], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "80a08b49-e665-4b05-93b3-f4354d107800"}, {"parameters": {"operation": "update", "companyId": "={{$node[\"Copper\"].json[\"id\"]}}", "updateFields": {"address": {"addressFields": {"street": "UpdateStreet", "city": "UpdatedCity", "state": "UpdateState", "postal_code": "1002", "country": "UpdateCountry"}}, "name": "=Updated{{$node[\"Copper1\"].json[\"name\"]}}"}}, "name": "Copper2", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [800, 130], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "c3ada9b0-26af-4531-b932-e2f0a4c672d9"}, {"parameters": {"operation": "getAll", "limit": 1, "filterFields": {"name": "={{$node[\"Copper2\"].json[\"name\"]}}"}}, "name": "Copper3", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [950, 130], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "ee32ed20-d5f4-40e3-8288-a678df7c9c98"}, {"parameters": {"operation": "delete", "companyId": "={{$node[\"Copper\"].json[\"id\"]}}"}, "name": "Copper4", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [1100, 130], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "b4c3643c-e8d5-41ce-87fd-9edd54201956"}, {"parameters": {"resource": "customerSource", "limit": 1}, "name": "Copper5", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [650, 440], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "ec0a392e-77a5-4c30-a1f1-31554e924160"}, {"parameters": {"resource": "person", "name": "=Person{{Date.now()}}", "additionalFields": {"details": "Person description - Test"}}, "name": "Copper6", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [500, 290], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "de249aff-db75-4937-a878-25ad37c5b160"}, {"parameters": {"resource": "person", "operation": "get", "personId": "={{$node[\"Copper6\"].json[\"id\"]}}"}, "name": "Copper7", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [750, 290], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "2ab13b8a-2012-4b79-8b66-c01e2e539168"}, {"parameters": {"resource": "person", "operation": "update", "personId": "={{$node[\"Copper6\"].json[\"id\"]}}", "updateFields": {"name": "=Updated{{$node[\"Copper7\"].json[\"name\"]}}"}}, "name": "Copper8", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [900, 290], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "036bce7e-3450-4dc5-a8d9-09031be7b283"}, {"parameters": {"resource": "person", "operation": "getAll", "limit": 1, "filterFields": {}}, "name": "Copper9", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [1050, 290], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "0ee23c1e-c3fc-44d2-ab7f-962fcb519d5f"}, {"parameters": {"resource": "person", "operation": "delete", "personId": "={{$node[\"Copper6\"].json[\"id\"]}}"}, "name": "Copper10", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [1200, 290], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "23ceb77e-bc3d-4e38-8d9c-339149e82555"}, {"parameters": {"resource": "project", "name": "=Project{{Date.now()}}", "additionalFields": {"details": "Detail projects - Test", "status": "Open"}}, "name": "Copper11", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [500, 590], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "987fcbb0-1be5-49b7-88cc-706e091200c4"}, {"parameters": {"resource": "project", "operation": "get", "projectId": "={{$node[\"Copper11\"].json[\"id\"]}}"}, "name": "Copper12", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [650, 590], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "8c6eab76-1955-46f8-99c0-f6e7cba6d089"}, {"parameters": {"resource": "project", "operation": "update", "projectId": "={{$node[\"Copper11\"].json[\"id\"]}}", "updateFields": {"details": "Completed Update", "name": "=Upated{{$node[\"Copper12\"].json[\"name\"]}}", "status": "Completed"}}, "name": "Copper13", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [800, 590], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "bb59c271-79e0-48f1-8f7c-2495fa913b16"}, {"parameters": {"resource": "project", "operation": "getAll", "limit": 1, "filterFields": {}}, "name": "Copper14", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [950, 590], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "322d58e4-799a-43f4-893a-93ef541e1250"}, {"parameters": {"resource": "project", "operation": "delete", "projectId": "={{$node[\"Copper11\"].json[\"id\"]}}"}, "name": "Copper15", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [1100, 590], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "3319110f-6cee-48da-a712-02e462c6eb63"}, {"parameters": {"resource": "task", "name": "=Task{{Date.now()}}", "additionalFields": {"details": "Task description", "priority": "High", "status": "Open"}}, "name": "Copper16", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [500, 740], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "cf53821f-74de-44c4-9e18-da30dbebba9e"}, {"parameters": {"resource": "task", "operation": "get", "taskId": "={{$node[\"Copper16\"].json[\"id\"]}}"}, "name": "Copper17", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [650, 740], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "564d21cd-fc4c-4b55-8068-bd4c559aa73d"}, {"parameters": {"resource": "task", "operation": "update", "taskId": "={{$node[\"Copper16\"].json[\"id\"]}}", "updateFields": {"details": "Update task description", "name": "=Updated{{$node[\"Copper17\"].json[\"name\"]}}", "priority": "None", "status": "Completed"}}, "name": "Copper18", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [800, 740], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "1e015bc3-f684-42da-80ff-d523ba36fa5d"}, {"parameters": {"resource": "task", "operation": "getAll", "limit": 1, "filterFields": {"project_ids": "={{$node[\"Copper18\"].json[\"id\"]}},"}}, "name": "Copper19", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [950, 740], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "e8986a4b-02b9-4596-9512-078f88be2431"}, {"parameters": {"resource": "task", "operation": "delete", "taskId": "={{$node[\"Copper16\"].json[\"id\"]}}"}, "name": "Copper20", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [1100, 740], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "b5cd7438-3115-4146-8607-34b67476f5b3"}, {"parameters": {"resource": "user", "limit": 1}, "name": "Copper21", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [500, -10], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "94cdc07a-0c7e-4f25-a1c0-e1385358a455"}, {"parameters": {"resource": "opportunity", "name": "=Opportunity{{Date.now()}}", "customerSourceId": "={{$node[\"Copper5\"].json[\"id\"]}}", "primaryContactId": "={{$node[\"Copper6\"].json[\"id\"]}}"}, "name": "Copper22", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [800, 440], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "c8d1ed81-1892-4687-9506-4df121ba7b53"}, {"parameters": {"resource": "opportunity", "operation": "get", "opportunityId": "={{$node[\"Copper22\"].json[\"id\"]}}"}, "name": "Copper23", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [950, 440], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "8aa793b5-49d9-4329-9f59-95d265d9df92"}, {"parameters": {"resource": "opportunity", "operation": "update", "opportunityId": "={{$node[\"Copper22\"].json[\"id\"]}}", "updateFields": {"name": "=Updated{{$node[\"Copper23\"].json[\"name\"]}}"}}, "name": "Copper24", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [1100, 440], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "64ded247-0c6f-4256-9689-dbc4738df63a"}, {"parameters": {"resource": "opportunity", "operation": "getAll", "limit": 1, "filterFields": {}}, "name": "Copper25", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [1250, 440], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "3834c3ed-308c-46c7-9aa6-3bc441d11897"}, {"parameters": {"resource": "opportunity", "operation": "delete", "opportunityId": "={{$node[\"Copper22\"].json[\"id\"]}}"}, "name": "Copper26", "type": "n8n-nodes-base.copper", "typeVersion": 1, "position": [1400, 440], "credentials": {"copperApi": {"id": "214", "name": "Copper API creds"}}, "id": "6c2b484c-d78f-412a-ac33-eb5637fd30fa"}], "connections": {"Copper": {"main": [[{"node": "Copper1", "type": "main", "index": 0}]]}, "Copper1": {"main": [[{"node": "Copper2", "type": "main", "index": 0}]]}, "Copper2": {"main": [[{"node": "Copper3", "type": "main", "index": 0}]]}, "Copper3": {"main": [[{"node": "Copper4", "type": "main", "index": 0}]]}, "Copper6": {"main": [[{"node": "Copper5", "type": "main", "index": 0}]]}, "Copper7": {"main": [[{"node": "Copper8", "type": "main", "index": 0}]]}, "Copper8": {"main": [[{"node": "Copper9", "type": "main", "index": 0}]]}, "Copper9": {"main": [[{"node": "Copper10", "type": "main", "index": 0}]]}, "Copper11": {"main": [[{"node": "Copper12", "type": "main", "index": 0}]]}, "Copper12": {"main": [[{"node": "Copper13", "type": "main", "index": 0}]]}, "Copper13": {"main": [[{"node": "Copper14", "type": "main", "index": 0}]]}, "Copper14": {"main": [[{"node": "Copper15", "type": "main", "index": 0}]]}, "Copper16": {"main": [[{"node": "Copper17", "type": "main", "index": 0}]]}, "Copper17": {"main": [[{"node": "Copper18", "type": "main", "index": 0}]]}, "Copper18": {"main": [[{"node": "Copper19", "type": "main", "index": 0}]]}, "Copper19": {"main": [[{"node": "Copper20", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Copper", "type": "main", "index": 0}, {"node": "Copper6", "type": "main", "index": 0}, {"node": "Copper11", "type": "main", "index": 0}, {"node": "Copper16", "type": "main", "index": 0}, {"node": "Copper21", "type": "main", "index": 0}]]}, "Copper5": {"main": [[{"node": "Copper22", "type": "main", "index": 0}]]}, "Copper22": {"main": [[{"node": "Copper23", "type": "main", "index": 0}]]}, "Copper23": {"main": [[{"node": "Copper24", "type": "main", "index": 0}]]}, "Copper24": {"main": [[{"node": "Copper25", "type": "main", "index": 0}]]}, "Copper25": {"main": [[{"node": "Copper26", "type": "main", "index": 0}]]}, "Copper26": {"main": [[{"node": "Copper7", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}