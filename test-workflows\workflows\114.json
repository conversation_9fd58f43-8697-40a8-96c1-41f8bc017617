{"createdAt": "2021-03-09T20:05:11.480Z", "updatedAt": "2021-03-22T10:44:13.980Z", "id": "114", "name": "Monday:Board:create get getAll archive:BoardColumn:create getAll:BoardGroup:create delete getAll:BoardItem:create addUpdate changeColumnValue get getAll getByColumnValue move delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [200, 300], "id": "f7c60ae7-b68c-4251-9778-4d7a1f033285"}, {"parameters": {"name": "=Board{{Date.now()}}", "kind": "private", "additionalFields": {}}, "name": "Monday.com", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [440, 280], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "839f14d0-14de-40e9-81c8-08810b6edf70"}, {"parameters": {"operation": "get", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}"}, "name": "Monday.com1", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [600, 280], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "bbf1c663-e121-4d6c-9afc-a6185d15d45a"}, {"parameters": {"operation": "getAll", "limit": 1}, "name": "Monday.com2", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [750, 280], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "19e62461-a13a-4ed5-984d-48c932f3c673"}, {"parameters": {"operation": "archive", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}"}, "name": "Monday.com3", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [3110, 270], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "1f5c07fc-54f7-47fd-9c32-5c5d58013755"}, {"parameters": {"resource": "boardColumn", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}", "title": "=Column{{Date.now()}}", "columnType": "checkbox", "additionalFields": {}}, "name": "Monday.com4", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [900, 0], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "e96e4955-0dd8-45a9-90ae-b8f0b2cf4cb2"}, {"parameters": {"resource": "boardColumn", "operation": "getAll", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}"}, "name": "Monday.com5", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [2850, -50], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "b009f62b-41a1-46f1-8796-30fa18e74c5c"}, {"parameters": {"resource": "boardGroup", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}", "name": "=Group{{Date.now()}}"}, "name": "Monday.com6", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [1200, 50], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "530e0e10-cba4-41c8-8e8c-203f451501b2"}, {"parameters": {"resource": "boardGroup", "operation": "delete", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}", "groupId": "={{$node[\"Monday.com6\"].json[\"id\"]}}"}, "name": "Monday.com7", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [2540, 50], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "27c609e7-c3e6-4e4a-af47-08a9a34ce21c"}, {"parameters": {"resource": "boardGroup", "operation": "getAll", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}"}, "name": "Monday.com8", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [2690, 50], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "405a5103-b9ce-4297-90d8-d5fa8fd5852f"}, {"parameters": {"resource": "boardItem", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}", "groupId": "={{$node[\"Monday.com6\"].json[\"id\"]}}", "name": "=Item{{Date.now()}}", "additionalFields": {}}, "name": "Monday.com9", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [1350, 150], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "dc5e88cf-04a4-4858-927a-2e08c4b9ceb8"}, {"parameters": {"resource": "boardItem", "operation": "addUpdate", "itemId": "={{$node[\"Monday.com9\"].json[\"id\"]}}", "value": "=Update{{Date.now()}}"}, "name": "Monday.com10", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [1500, 150], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "1d508527-3c1c-4c7c-a6f6-19ed2e674eb8"}, {"parameters": {"resource": "boardItem", "operation": "changeColumnValue", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}", "itemId": "={{$node[\"Monday.com9\"].json[\"id\"]}}", "columnId": "={{$node[\"Monday.com4\"].json[\"id\"]}}", "value": "={{JSON.stringify({\"checked\": \"true\"});}}"}, "name": "Monday.com11", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [1650, 150], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "f03bca62-ee0f-4598-8833-26d9ba2f83fb"}, {"parameters": {"resource": "boardItem", "operation": "get", "itemId": "={{$node[\"Monday.com9\"].json[\"id\"]}}"}, "name": "Monday.com12", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [1800, 150], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "b4a2cfef-83ea-430f-8b0c-3a7b3dafc5ed"}, {"parameters": {"resource": "boardItem", "operation": "getAll", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}", "groupId": "={{$node[\"Monday.com6\"].json[\"id\"]}}", "limit": 1}, "name": "Monday.com13", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [1950, 150], "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "549dc7a6-e611-4c75-9c37-40673a79bf39"}, {"parameters": {"resource": "boardItem", "operation": "getByColumnValue", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}", "columnId": "={{$node[\"Monday.com4\"].json[\"id\"]}}", "columnValue": "\"{\"checked\":\"true\"}\"", "limit": 1}, "name": "Monday.com14", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [2100, 150], "alwaysOutputData": true, "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "7026f1b0-8014-43ae-930d-a1c7db989163"}, {"parameters": {"resource": "boardItem", "operation": "move", "boardId": "={{$node[\"Monday.com\"].json[\"id\"]}}", "itemId": "={{$node[\"Monday.com9\"].json[\"id\"]}}", "groupId": "={{$node[\"Monday.com6\"].json[\"id\"]}}"}, "name": "Monday.com15", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [2250, 150], "alwaysOutputData": true, "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "596fc3f9-0d39-4f7f-864b-32a6640e7ff0"}, {"parameters": {"resource": "boardItem", "operation": "delete", "itemId": "={{$node[\"Monday.com9\"].json[\"id\"]}}"}, "name": "Monday.com16", "type": "n8n-nodes-base.mondayCom", "typeVersion": 1, "position": [2400, 150], "alwaysOutputData": true, "credentials": {"mondayComApi": {"id": "113", "name": "Monday.com API creds"}}, "id": "6080f346-15d2-484f-8cfa-44b93dc93213"}, {"parameters": {"mode": "wait"}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [2960, 120], "id": "7ca43678-0fea-4fc4-85c5-af1dc02be4d2"}], "connections": {"Monday.com": {"main": [[{"node": "Monday.com1", "type": "main", "index": 0}]]}, "Monday.com1": {"main": [[{"node": "Monday.com2", "type": "main", "index": 0}]]}, "Monday.com2": {"main": [[{"node": "Monday.com4", "type": "main", "index": 0}]]}, "Monday.com4": {"main": [[{"node": "Monday.com6", "type": "main", "index": 0}]]}, "Monday.com6": {"main": [[{"node": "Monday.com9", "type": "main", "index": 0}]]}, "Monday.com7": {"main": [[{"node": "Monday.com8", "type": "main", "index": 0}]]}, "Monday.com9": {"main": [[{"node": "Monday.com10", "type": "main", "index": 0}]]}, "Monday.com10": {"main": [[{"node": "Monday.com11", "type": "main", "index": 0}]]}, "Monday.com11": {"main": [[{"node": "Monday.com12", "type": "main", "index": 0}]]}, "Monday.com12": {"main": [[{"node": "Monday.com13", "type": "main", "index": 0}]]}, "Monday.com13": {"main": [[{"node": "Monday.com14", "type": "main", "index": 0}]]}, "Monday.com14": {"main": [[{"node": "Monday.com15", "type": "main", "index": 0}]]}, "Monday.com15": {"main": [[{"node": "Monday.com16", "type": "main", "index": 0}]]}, "Monday.com16": {"main": [[{"node": "Monday.com7", "type": "main", "index": 0}]]}, "Monday.com8": {"main": [[{"node": "Monday.com5", "type": "main", "index": 0}]]}, "Monday.com5": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Monday.com3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Monday.com", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}