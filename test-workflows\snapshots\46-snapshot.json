{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1678116858527, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Google Books": [{"startTime": 1678116858528, "executionTime": 1159, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "books#volume", "id": "tvfyz-4JILwC", "etag": "o0PPfLNFxNc", "selfLink": "https://www.googleapis.com/books/v1/volumes/tvfyz-4JILwC", "volumeInfo": {"object": true}, "saleInfo": {"object": true}, "accessInfo": {"object": true}, "searchInfo": {"object": true}}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#volume", "id": "_CAdswEACAAJ", "etag": "Q7ewsaDZHyY", "selfLink": "https://www.googleapis.com/books/v1/volumes/_CAdswEACAAJ", "volumeInfo": {"object": true}, "saleInfo": {"object": true}, "accessInfo": {"object": true}, "searchInfo": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Google Books2": [{"startTime": 1678116859687, "executionTime": 328, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "books#bookshelf", "id": 7, "title": "My Google eBooks", "access": "PRIVATE", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 1, "title": "Purchased", "access": "PRIVATE", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 5, "title": "Reviewed", "access": "PUBLIC", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 6, "title": "Recently viewed", "access": "PRIVATE", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 9, "title": "Browsing history", "access": "PRIVATE", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 0, "title": "Favorites", "access": "PUBLIC", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 3, "title": "Reading now", "access": "PUBLIC", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 2, "title": "To read", "access": "PUBLIC", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 4, "title": "Have read", "access": "PUBLIC", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 8, "title": "Books for you", "access": "PRIVATE", "updated": "2023-03-06T15:34:19.000Z", "created": "2023-03-06T15:34:19.000Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T15:34:19.000Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 0}}]]}}], "Google Books1": [{"startTime": 1678116860015, "executionTime": 818, "source": [{"previousNode": "Google Books"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "books#volume", "id": "tvfyz-4JILwC", "etag": "6O9J2CN53Ew", "selfLink": "https://www.googleapis.com/books/v1/volumes/tvfyz-4JILwC", "volumeInfo": {"object": true}, "userInfo": {"object": true}, "saleInfo": {"object": true}, "accessInfo": {"object": true}}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#volume", "id": "_CAdswEACAAJ", "etag": "jAT797c77bA", "selfLink": "https://www.googleapis.com/books/v1/volumes/_CAdswEACAAJ", "volumeInfo": {"object": true}, "userInfo": {"object": true}, "saleInfo": {"object": true}, "accessInfo": {"object": true}}, "pairedItem": {"item": 1}}]]}}], "Google Books3": [{"startTime": 1678116860834, "executionTime": 3343, "source": [{"previousNode": "Google Books2"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 1}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 2}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 3}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 4}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 5}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 6}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 7}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 8}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 9}}, {"json": {"kind": "books#bookshelf", "id": 1001, "title": "FixedBookShelf", "description": "This a bookshelf create for the test workflows", "access": "PRIVATE", "updated": "2023-03-06T11:56:15.625Z", "created": "2023-03-06T11:56:15.625Z", "volumeCount": 0, "volumesLastUpdated": "2023-03-06T11:56:15.622Z"}, "pairedItem": {"item": 10}}]]}}], "Google Books4": [{"startTime": 1678116864177, "executionTime": 615, "source": [{"previousNode": "Google Books1"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}, {"json": {}, "pairedItem": {"item": 1}}]]}}], "Google Books5": [{"startTime": 1678116864792, "executionTime": 565, "source": [{"previousNode": "Google Books4"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}, {"json": {}, "pairedItem": {"item": 1}}]]}}], "Google Books6": [{"startTime": 1678116865358, "executionTime": 602, "source": [{"previousNode": "Google Books5"}], "executionStatus": "success", "data": {"main": [[{"json": {"kind": "books#volume", "id": "_CAdswEACAAJ", "etag": "9WPBLHtCNPI", "selfLink": "https://www.googleapis.com/books/v1/volumes/_CAdswEACAAJ", "volumeInfo": {"object": true}, "userInfo": {"object": true}, "saleInfo": {"object": true}, "accessInfo": {"object": true}}, "pairedItem": {"item": 0}}, {"json": {"kind": "books#volume", "id": "_CAdswEACAAJ", "etag": "QEA8HzqsC+4", "selfLink": "https://www.googleapis.com/books/v1/volumes/_CAdswEACAAJ", "volumeInfo": {"object": true}, "userInfo": {"object": true}, "saleInfo": {"object": true}, "accessInfo": {"object": true}}, "pairedItem": {"item": 1}}]]}}], "Google Books7": [{"startTime": 1678116865961, "executionTime": 668, "source": [{"previousNode": "Google Books6"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}, {"json": {}, "pairedItem": {"item": 1}}]]}}], "Google Books8": [{"startTime": 1678116866629, "executionTime": 562, "source": [{"previousNode": "Google Books7"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}, {"json": {}, "pairedItem": {"item": 1}}]]}}]}, "lastNodeExecuted": "Google Books8"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-03-06T15:34:18.524Z", "stoppedAt": "2023-03-06T15:34:27.191Z", "status": "running", "finished": true}