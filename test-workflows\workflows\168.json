{"createdAt": "2021-04-13T14:55:34.780Z", "updatedAt": "2021-05-26T08:10:22.923Z", "id": "168", "name": "Demio:Event:getAll get register:Report:get", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [400, 480], "id": "47dbd966-77aa-4bda-96ed-e97f6a5981fd"}, {"parameters": {"operation": "getAll", "limit": 1, "filters": {}}, "name": "De<PERSON>", "type": "n8n-nodes-base.demio", "typeVersion": 1, "position": [650, 420], "credentials": {"demioApi": {"id": "135", "name": "Demio API creds"}}, "id": "57e082a8-354b-45a4-942d-11f7227fc3ce"}, {"parameters": {"eventId": "={{$node[\"Demio\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Demio1", "type": "n8n-nodes-base.demio", "typeVersion": 1, "position": [790, 420], "credentials": {"demioApi": {"id": "135", "name": "Demio API creds"}}, "id": "1e57d585-0f82-4111-8c63-f5b4e66cef5e"}, {"parameters": {"operation": "register", "eventId": "={{$node[\"Demio\"].json[\"id\"]}}", "firstName": "=Name{{Date.now()}}", "email": "=Fake{{Date.now()}}@email.com", "additionalFields": {}}, "name": "Demio2", "type": "n8n-nodes-base.demio", "typeVersion": 1, "position": [940, 420], "credentials": {"demioApi": {"id": "135", "name": "Demio API creds"}}, "id": "517f9682-cda3-406e-bf92-668fcc9feccb"}, {"parameters": {"resource": "report", "eventId": 400538, "dateId": 1967450, "filters": {}}, "name": "Demio3", "type": "n8n-nodes-base.demio", "typeVersion": 1, "position": [650, 570], "credentials": {"demioApi": {"id": "135", "name": "Demio API creds"}}, "id": "3404303d-775a-4445-9458-a77f408726a7"}], "connections": {"Demio": {"main": [[{"node": "Demio1", "type": "main", "index": 0}]]}, "Demio1": {"main": [[{"node": "Demio2", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "De<PERSON>", "type": "main", "index": 0}, {"node": "Demio3", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}