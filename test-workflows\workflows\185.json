{"createdAt": "2021-04-26T16:57:16.077Z", "updatedAt": "2021-05-20T16:55:37.548Z", "id": "185", "name": "Salesforce:Lead:create get addNote addToCampaign getAll getSummary update delete:Contact:create get addNote addToCampaign getAll getSummary update delete:CustomObject:create get getAll update delete:Flow:getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "3e759fa8-73f9-4851-968b-65c1843c646c"}, {"parameters": {"resource": "contact", "lastname": "=Contact{{Date.now()}}", "additionalFields": {}}, "name": "Salesforce", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [450, 230], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "0ae37230-15ba-4ff3-a4af-373fa3a1b0b4"}, {"parameters": {"resource": "contact", "operation": "get", "contactId": "={{$node[\"Salesforce\"].json[\"id\"]}}"}, "name": "Salesforce1", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [600, 230], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "a2dc5829-0aee-495e-a7e2-a8db3b8eacc5"}, {"parameters": {"resource": "contact", "operation": "addNote", "contactId": "={{$node[\"Salesforce\"].json[\"id\"]}}", "title": "=Note{{Date.now()}}", "options": {}}, "name": "Salesforce2", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [940, 230], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "75d1b33c-cb4d-429e-835d-06e3b59cecff"}, {"parameters": {"resource": "contact", "operation": "getSummary"}, "name": "Salesforce3", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1090, 230], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "6a0bda2d-3ba5-409f-b5c2-d1cce8e13206"}, {"parameters": {"resource": "contact", "operation": "getAll", "limit": 1, "options": {}}, "name": "Salesforce4", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1400, 230], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "bf879abe-b86e-44f9-819a-96dcde8ed019"}, {"parameters": {"resource": "contact", "operation": "addToCampaign", "contactId": "={{$node[\"Salesforce\"].json[\"id\"]}}", "campaignId": "70109000000TU12AAG", "options": {}}, "name": "Salesforce5", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1550, 230], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "a00bc642-9a77-4109-9de3-be96c3d9c615"}, {"parameters": {"resource": "contact", "operation": "update", "contactId": "={{$node[\"Salesforce\"].json[\"id\"]}}", "updateFields": {"title": "=UpdatedTitle{{Date.now()}}"}}, "name": "Salesforce6", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1910, 230], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "f7354012-b9c7-4a0b-9ca6-d627443b24c5"}, {"parameters": {"resource": "contact", "operation": "delete", "contactId": "={{$node[\"Salesforce\"].json[\"id\"]}}"}, "name": "Salesforce7", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [2060, 230], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "d1552ed3-73d6-4f8d-801c-d24e594044b7"}, {"parameters": {"resource": "customObject", "customObject": "CustomObjectFixed__c", "customFieldsUi": {"customFieldsValues": [{"fieldId": "Name", "value": "TestCustomObjectFixed"}]}}, "name": "Salesforce8", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [460, 400], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "1ca8c1bc-5e2b-45c5-ab7a-33b766fa906b"}, {"parameters": {"resource": "customObject", "operation": "get", "customObject": "CustomObjectFixed__c", "recordId": "={{$node[\"Salesforce8\"].json[\"id\"]}}"}, "name": "Salesforce9", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [810, 400], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "7ec96ddd-97b2-48c1-ba74-f0219d9f8df6"}, {"parameters": {"resource": "customObject", "operation": "getAll", "customObject": "CustomObjectFixed__c", "limit": 1, "options": {}}, "name": "Salesforce10", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [950, 400], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "655e131b-ce30-4408-90c4-33c2bbf2509d"}, {"parameters": {"resource": "customObject", "operation": "update", "customObject": "CustomObjectFixed__c", "recordId": "={{$node[\"Salesforce8\"].json[\"id\"]}}", "customFieldsUi": {"customFieldsValues": [{"fieldId": "Name", "value": "UpdatedCustomObjectFixed"}]}}, "name": "Salesforce11", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1320, 400], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "a8bbc9dc-095e-4977-8e7e-ab51105e067e"}, {"parameters": {"resource": "customObject", "operation": "delete", "customObject": "CustomObjectFixed__c", "recordId": "={{$node[\"Salesforce8\"].json[\"id\"]}}"}, "name": "Salesforce12", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1470, 400], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "079ed153-25b5-459d-8fa9-09ed6b6839a4"}, {"parameters": {"resource": "flow", "operation": "getAll", "limit": 1}, "name": "Salesforce13", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [460, 560], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "f869a8a4-f0ac-4ad6-a594-2d51099aa226"}, {"parameters": {"resource": "flow", "apiName": "={{$node[\"Salesforce13\"].json[\"name\"]}}"}, "name": "Salesforce14", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [600, 560], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "disabled": true, "id": "f476766b-2817-4b3a-9220-21b5852609e2"}, {"parameters": {"company": "n8n", "lastname": "=LastName{{Date.now()}}", "additionalFields": {}}, "name": "Salesforce15", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [450, 50], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "2881bb5a-12ff-41b6-9624-82bbadf392e2"}, {"parameters": {"operation": "get", "leadId": "={{$node[\"Salesforce15\"].json[\"id\"]}}"}, "name": "Salesforce16", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [600, 50], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "79861ab0-f376-48df-aeba-08c297c7070f"}, {"parameters": {"operation": "addNote", "leadId": "={{$node[\"Salesforce15\"].json[\"id\"]}}", "title": "LeadNote", "options": {}}, "name": "Salesforce17", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [940, 50], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "19fd1281-ef46-4aa2-9b21-0c767bf2e050"}, {"parameters": {"operation": "getSummary"}, "name": "Salesforce18", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1090, 50], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "f1b56cfe-41c8-4f8b-9ce5-ba48744907fd"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}}, "name": "Salesforce19", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1400, 50], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "b80c3c9c-9f27-459c-b4c7-8d581923dced"}, {"parameters": {"operation": "addToCampaign", "leadId": "={{$node[\"Salesforce15\"].json[\"id\"]}}", "campaignId": "70109000000TU12AAG", "options": {}}, "name": "Salesforce20", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1550, 50], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "b7fa794c-495a-4d41-9416-9b318522e236"}, {"parameters": {"operation": "update", "leadId": "={{$node[\"Salesforce15\"].json[\"id\"]}}", "updateFields": {"description": "Updated Description"}}, "name": "Salesforce21", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1910, 50], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "c26d4856-782a-47e9-aa17-aecb7705610a"}, {"parameters": {"operation": "delete", "leadId": "={{$node[\"Salesforce15\"].json[\"id\"]}}"}, "name": "Salesforce22", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [2060, 50], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "02fc2ba8-33ae-4ffe-9e18-ed113a5b75e6"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second", "type": "n8n-nodes-base.function", "position": [770, 50], "typeVersion": 1, "id": "c004a09e-09fd-4ac2-8d5c-270a15b204b1"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second1", "type": "n8n-nodes-base.function", "position": [770, 230], "typeVersion": 1, "id": "4939f605-622d-4f84-8da2-6565fc63b42f"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second2", "type": "n8n-nodes-base.function", "position": [1240, 50], "typeVersion": 1, "id": "e44a9ad5-a103-49da-956c-53ff4993450c"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second3", "type": "n8n-nodes-base.function", "position": [1250, 230], "typeVersion": 1, "id": "75c555b9-1b6f-426a-9dab-b691918843a2"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second4", "type": "n8n-nodes-base.function", "position": [1730, 50], "typeVersion": 1, "id": "523558db-379a-4eac-8ce0-03e3ce08ff6a"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second5", "type": "n8n-nodes-base.function", "position": [1730, 230], "typeVersion": 1, "id": "077e1d54-864f-47f9-b248-a5b482946e13"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second6", "type": "n8n-nodes-base.function", "position": [1150, 400], "typeVersion": 1, "id": "d6328f6e-5944-4f30-8911-1f691fbfbd4c"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second7", "type": "n8n-nodes-base.function", "position": [620, 400], "typeVersion": 1, "id": "feb9fe8d-176e-442e-85e5-f58a42603a35"}], "connections": {"Salesforce": {"main": [[{"node": "Salesforce1", "type": "main", "index": 0}]]}, "Salesforce1": {"main": [[{"node": "Sleep 0.5 second1", "type": "main", "index": 0}]]}, "Salesforce2": {"main": [[{"node": "Salesforce3", "type": "main", "index": 0}]]}, "Salesforce3": {"main": [[{"node": "Sleep 0.5 second3", "type": "main", "index": 0}]]}, "Salesforce4": {"main": [[{"node": "Salesforce5", "type": "main", "index": 0}]]}, "Salesforce5": {"main": [[{"node": "Sleep 0.5 second5", "type": "main", "index": 0}]]}, "Salesforce6": {"main": [[{"node": "Salesforce7", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Salesforce", "type": "main", "index": 0}, {"node": "Salesforce8", "type": "main", "index": 0}, {"node": "Salesforce13", "type": "main", "index": 0}, {"node": "Salesforce15", "type": "main", "index": 0}]]}, "Salesforce8": {"main": [[{"node": "Sleep 0.5 second7", "type": "main", "index": 0}]]}, "Salesforce9": {"main": [[{"node": "Salesforce10", "type": "main", "index": 0}]]}, "Salesforce10": {"main": [[{"node": "Sleep 0.5 second6", "type": "main", "index": 0}]]}, "Salesforce11": {"main": [[{"node": "Salesforce12", "type": "main", "index": 0}]]}, "Salesforce13": {"main": [[{"node": "Salesforce14", "type": "main", "index": 0}]]}, "Salesforce15": {"main": [[{"node": "Salesforce16", "type": "main", "index": 0}]]}, "Salesforce16": {"main": [[{"node": "Sleep 0.5 second", "type": "main", "index": 0}]]}, "Salesforce17": {"main": [[{"node": "Salesforce18", "type": "main", "index": 0}]]}, "Salesforce18": {"main": [[{"node": "Sleep 0.5 second2", "type": "main", "index": 0}]]}, "Salesforce19": {"main": [[{"node": "Salesforce20", "type": "main", "index": 0}]]}, "Salesforce20": {"main": [[{"node": "Sleep 0.5 second4", "type": "main", "index": 0}]]}, "Salesforce21": {"main": [[{"node": "Salesforce22", "type": "main", "index": 0}]]}, "Sleep 0.5 second4": {"main": [[{"node": "Salesforce21", "type": "main", "index": 0}]]}, "Sleep 0.5 second5": {"main": [[{"node": "Salesforce6", "type": "main", "index": 0}]]}, "Sleep 0.5 second2": {"main": [[{"node": "Salesforce19", "type": "main", "index": 0}]]}, "Sleep 0.5 second3": {"main": [[{"node": "Salesforce4", "type": "main", "index": 0}]]}, "Sleep 0.5 second": {"main": [[{"node": "Salesforce17", "type": "main", "index": 0}]]}, "Sleep 0.5 second1": {"main": [[{"node": "Salesforce2", "type": "main", "index": 0}]]}, "Sleep 0.5 second6": {"main": [[{"node": "Salesforce11", "type": "main", "index": 0}]]}, "Sleep 0.5 second7": {"main": [[{"node": "Salesforce9", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}