{"createdAt": "2021-04-29T08:53:09.807Z", "updatedAt": "2021-05-12T17:31:21.682Z", "id": "189", "name": "Affinity:Organization:create get update delete getAll:List:getAll get:ListEntry:create get getAll delete:Person:create get update getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "87d46406-85dc-49d6-bf83-b69e094d6873"}, {"parameters": {"name": "=TESTCOMPANY{{Date.now()}}", "domain": "test.com", "additionalFields": {}}, "name": "Affinity", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [500, 300], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "1be8b362-0d29-48ef-8f70-f98b408ac154"}, {"parameters": {"operation": "get", "organizationId": "={{$node[\"Affinity\"].json[\"id\"]}}", "options": {}}, "name": "Affinity1", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [650, 300], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "5797c0f2-ace5-41ef-aabf-1104f582ae02"}, {"parameters": {"operation": "update", "organizationId": "={{$node[\"Affinity\"].json[\"id\"]}}", "updateFields": {"name": "={{$node[\"Affinity1\"].json[\"name\"]}}-UPDATED"}}, "name": "Affinity2", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [800, 300], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "1a74bdc8-4e0e-4d9c-9af0-4e80dba7a290"}, {"parameters": {"operation": "delete", "organizationId": "={{$node[\"Affinity\"].json[\"id\"]}}"}, "name": "Affinity3", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [950, 300], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "781cad97-11a9-47ba-8de2-0b1863513c0f"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}}, "name": "Affinity4", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [500, 450], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "d803cdfd-b2ea-4f74-9839-56c26276a9d4"}, {"parameters": {"resource": "list", "operation": "getAll", "limit": 1}, "name": "Affinity5", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [500, 150], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "d2f43144-3321-4b65-9aed-91ed98d8b26d"}, {"parameters": {"resource": "list", "listId": "={{$node[\"Affinity5\"].json[\"id\"]}}"}, "name": "Affinity6", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [650, 150], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "7f3f2a9b-d82c-451f-bf94-785afba42af9"}, {"parameters": {"resource": "person", "firstName": "=Fname_{{(new Date).toISOString()}}", "lastName": "=Lname_{{(new Date).toISOString()}}", "additionalFields": {}}, "name": "Affinity7", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [500, 600], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "a7a2a691-028c-4c01-a8ed-392e2c026f94"}, {"parameters": {"resource": "person", "operation": "get", "personId": "={{$node[\"Affinity7\"].json[\"id\"]}}", "options": {}}, "name": "Affinity8", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [650, 600], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "12399f87-e27c-443b-809e-812eaa0e41fb"}, {"parameters": {"resource": "person", "operation": "update", "personId": "={{$node[\"Affinity7\"].json[\"id\"]}}", "updateFields": {}, "emails": ["=email_{{Date.now()}}@test.com"]}, "name": "Affinity9", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [800, 600], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "b28eb5fe-e6f9-487e-b947-469f21addca5"}, {"parameters": {"resource": "person", "operation": "getAll", "limit": 1, "options": {}}, "name": "Affinity10", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [950, 600], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "aadcf4a8-89eb-49b6-a1cf-36df4aff4c3b"}, {"parameters": {"resource": "person", "operation": "delete", "personId": "={{$node[\"Affinity7\"].json[\"id\"]}}"}, "name": "Affinity11", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [1100, 600], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "013b336c-1062-4f12-90f4-b3204fde09df"}, {"parameters": {"resource": "listEntry", "listId": 51365, "entityId": "={{$node[\"Affinity\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Affinity12", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [650, 450], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "99544ad9-75e7-4927-9070-a1d88df5631a"}, {"parameters": {"resource": "listEntry", "operation": "get", "listId": 51365, "listEntryId": "={{$node[\"Affinity12\"].json[\"id\"]}}"}, "name": "Affinity13", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [800, 450], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "8a4abe86-e2d3-4bb5-9f1a-2dc77c0668fa"}, {"parameters": {"resource": "listEntry", "operation": "getAll", "listId": 51365, "limit": 1}, "name": "Affinity14", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [950, 450], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "a1e54a4a-19a1-4705-bb5e-36a3074a987a"}, {"parameters": {"resource": "listEntry", "operation": "delete", "listId": 51365, "listEntryId": "={{$node[\"Affinity12\"].json[\"id\"]}}"}, "name": "Affinity15", "type": "n8n-nodes-base.affinity", "typeVersion": 1, "position": [1100, 450], "credentials": {"affinityApi": {"id": "152", "name": "Affinity API creds"}}, "id": "a1f2281d-3a7f-4632-9d76-8696703fda14"}], "connections": {"Affinity": {"main": [[{"node": "Affinity12", "type": "main", "index": 0}]]}, "Affinity1": {"main": [[{"node": "Affinity2", "type": "main", "index": 0}]]}, "Affinity2": {"main": [[{"node": "Affinity3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Affinity", "type": "main", "index": 0}, {"node": "Affinity4", "type": "main", "index": 0}, {"node": "Affinity5", "type": "main", "index": 0}, {"node": "Affinity7", "type": "main", "index": 0}]]}, "Affinity5": {"main": [[{"node": "Affinity6", "type": "main", "index": 0}]]}, "Affinity7": {"main": [[{"node": "Affinity8", "type": "main", "index": 0}]]}, "Affinity8": {"main": [[{"node": "Affinity9", "type": "main", "index": 0}]]}, "Affinity9": {"main": [[{"node": "Affinity10", "type": "main", "index": 0}]]}, "Affinity10": {"main": [[{"node": "Affinity11", "type": "main", "index": 0}]]}, "Affinity12": {"main": [[{"node": "Affinity13", "type": "main", "index": 0}]]}, "Affinity13": {"main": [[{"node": "Affinity14", "type": "main", "index": 0}]]}, "Affinity14": {"main": [[{"node": "Affinity15", "type": "main", "index": 0}]]}, "Affinity15": {"main": [[{"node": "Affinity1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}